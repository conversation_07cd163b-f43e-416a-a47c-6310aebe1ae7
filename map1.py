import matplotlib.pyplot as plt

# 设置画布
plt.figure(figsize=(18, 12))
plt.axis("off")
plt.title("人力资源服务业产业地图（汇报版）", fontsize=20, fontweight="bold", pad=20)

# 定义层级位置
y_positions = {
    "上游（金色）": 0.8,
    "中游（蓝色）": 0.55,
    "下游（绿色）": 0.3,
    "支撑（橙色）": 0.55
}

# 定义节点
upstream = [
    "招聘网站/平台\n智联招聘、前程无忧、猎聘",
    "测评工具\nSHL、科锐国际测评",
    "培训教材供应商\n高教社、企业培训教材商"
]

midstream = [
    "招聘服务\n前程无忧、BOSS直聘",
    "猎头服务\n海德思哲、光辉国际、科锐国际",
    "培训与发展\n新东方、华为大学、IBM培训",
    "劳务派遣\n外企德科、FESCO",
    "人事外包\n外企德科、任仕达",
    "灵活用工平台\n斗米、兼职猫、拉勾灵活用工"
]

downstream = [
    "企业客户\n大型央企、互联网企业、制造业公司",
    "政府/公共部门\n人社部、地方人社局"
]

support = [
    "政策法规\n劳动合同法、人社部政策",
    "资本/投资机构\n红杉资本、IDG资本",
    "科研院所\n中国社科院、清华大学劳动力市场研究中心",
    "行业协会\n中国人力资源开发研究会"
]

# 绘制矩形框函数
def draw_box(x, y, text, color):
    plt.gca().add_patch(plt.Rectangle((x-0.12, y-0.05), 0.24, 0.1, 
                                      facecolor=color, edgecolor="black", lw=1.5, alpha=0.9))
    plt.text(x, y, text, ha="center", va="center", fontsize=9, family="Microsoft YaHei")

# 绘制上游
x_positions = [-0.4, 0, 0.4]
for i, text in enumerate(upstream):
    draw_box(x_positions[i], y_positions["上游（金色）"], text, "#FFD700")

# 绘制中游
x_positions = [-0.6, -0.36, -0.12, 0.12, 0.36, 0.6]
for i, text in enumerate(midstream):
    draw_box(x_positions[i], y_positions["中游（蓝色）"], text, "#87CEEB")

# 绘制下游
x_positions = [-0.2, 0.2]
for i, text in enumerate(downstream):
    draw_box(x_positions[i], y_positions["下游（绿色）"], text, "#32CD32")

# 绘制支撑要素（放在右侧）
y_support_positions = [0.75, 0.65, 0.45, 0.35]
for i, text in enumerate(support):
    draw_box(0.9, y_support_positions[i], text, "#FF7F50")

# 画箭头（上游→中游→下游）
for x1 in [-0.4, 0, 0.4]:
    for x2 in [-0.6, -0.36, -0.12, 0.12, 0.36, 0.6]:
        plt.arrow(x1, 0.75, x2-x1, -0.15, head_width=0.02, head_length=0.02, 
                  fc="gray", ec="gray", alpha=0.5, length_includes_head=True)

for x1 in [-0.6, -0.36, -0.12, 0.12, 0.36, 0.6]:
    for x2 in [-0.2, 0.2]:
        plt.arrow(x1, 0.5, x2-x1, -0.2, head_width=0.02, head_length=0.02, 
                  fc="gray", ec="gray", alpha=0.5, length_includes_head=True)

# 画支撑要素箭头（支撑→中游）
for y in y_support_positions:
    for x2 in [-0.6, -0.36, -0.12, 0.12, 0.36, 0.6]:
        plt.arrow(0.78, y, x2-0.78, y_positions["中游（蓝色）"]-y, 
                  head_width=0.02, head_length=0.02, fc="gray", ec="gray", 
                  alpha=0.5, length_includes_head=True)

plt.show()
