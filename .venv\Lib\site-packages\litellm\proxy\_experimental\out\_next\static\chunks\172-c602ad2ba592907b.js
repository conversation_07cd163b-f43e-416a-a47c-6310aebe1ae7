"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[172],{57018:function(e,s,l){l.d(s,{Ct:function(){return t.Z},Dx:function(){return i.Z},Zb:function(){return r.Z},xv:function(){return n.Z},zx:function(){return a.Z}});var t=l(41649),a=l(20831),r=l(12514),n=l(84264),i=l(96761)},95704:function(e,s,l){l.d(s,{Dx:function(){return x.Z},RM:function(){return r.Z},SC:function(){return o.Z},Zb:function(){return t.Z},iA:function(){return a.Z},pj:function(){return n.Z},ss:function(){return i.Z},xs:function(){return c.Z},xv:function(){return d.Z}});var t=l(12514),a=l(21626),r=l(97214),n=l(28241),i=l(58834),c=l(69552),o=l(71876),d=l(84264),x=l(96761)},36172:function(e,s,l){l.d(s,{Z:function(){return R}});var t=l(57437),a=l(2265),r=l(99376),n=l(19250),i=l(8048),c=l(41649),o=l(20831),d=l(84264),x=l(89970),m=l(3810),u=l(23639),p=l(15424);let h=e=>e.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),g=e=>Object.entries(e).filter(e=>{let[s,l]=e;return s.startsWith("supports_")&&!0===l}).map(e=>{let[s]=e;return s}),j=e=>"$".concat((1e6*e).toFixed(2)),b=e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),v=function(e,s){let l=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=[{header:"Public Model Name",accessorKey:"model_group",enableSorting:!0,sortingFn:"alphanumeric",cell:e=>{let{row:l}=e,a=l.original;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.Z,{className:"font-medium text-sm",children:a.model_group}),(0,t.jsx)(x.Z,{title:"Copy model name",children:(0,t.jsx)(u.Z,{onClick:()=>s(a.model_group),className:"cursor-pointer text-gray-500 hover:text-blue-500 text-xs"})})]}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsx)(d.Z,{className:"text-xs text-gray-600",children:a.providers.join(", ")})})]})}},{header:"Provider",accessorKey:"providers",enableSorting:!0,sortingFn:(e,s)=>{let l=e.original.providers.join(", "),t=s.original.providers.join(", ");return l.localeCompare(t)},cell:e=>{let{row:s}=e,l=s.original;return(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.providers.slice(0,2).map(e=>(0,t.jsx)(m.Z,{color:"blue",className:"text-xs",children:e},e)),l.providers.length>2&&(0,t.jsxs)(d.Z,{className:"text-xs text-gray-500",children:["+",l.providers.length-2]})]})},meta:{className:"hidden md:table-cell"}},{header:"Mode",accessorKey:"mode",enableSorting:!0,sortingFn:"alphanumeric",cell:e=>{let{row:s}=e,l=s.original;return l.mode?(0,t.jsx)(c.Z,{color:"green",size:"sm",children:l.mode}):(0,t.jsx)(d.Z,{className:"text-gray-500",children:"-"})},meta:{className:"hidden lg:table-cell"}},{header:"Tokens",accessorKey:"max_input_tokens",enableSorting:!0,sortingFn:(e,s)=>(e.original.max_input_tokens||0)+(e.original.max_output_tokens||0)-((s.original.max_input_tokens||0)+(s.original.max_output_tokens||0)),cell:e=>{let{row:s}=e,l=s.original;return(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)(d.Z,{className:"text-xs",children:[l.max_input_tokens?b(l.max_input_tokens):"-"," / ",l.max_output_tokens?b(l.max_output_tokens):"-"]})})},meta:{className:"hidden lg:table-cell"}},{header:"Cost/1M",accessorKey:"input_cost_per_token",enableSorting:!0,sortingFn:(e,s)=>(e.original.input_cost_per_token||0)+(e.original.output_cost_per_token||0)-((s.original.input_cost_per_token||0)+(s.original.output_cost_per_token||0)),cell:e=>{let{row:s}=e,l=s.original;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d.Z,{className:"text-xs",children:l.input_cost_per_token?j(l.input_cost_per_token):"-"}),(0,t.jsx)(d.Z,{className:"text-xs text-gray-500",children:l.output_cost_per_token?j(l.output_cost_per_token):"-"})]})}},{header:"Features",accessorKey:"capabilities",enableSorting:!1,cell:e=>{let{row:s}=e,l=g(s.original),a=["green","blue","purple","orange","red","yellow"];return(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:0===l.length?(0,t.jsx)(d.Z,{className:"text-gray-500 text-xs",children:"-"}):l.map((e,s)=>(0,t.jsx)(c.Z,{color:a[s%a.length],size:"xs",children:h(e)},e))})}},{header:"Public",accessorKey:"is_public_model_group",enableSorting:!0,sortingFn:(e,s)=>(!0===e.original.is_public_model_group?1:0)-(!0===s.original.is_public_model_group?1:0),cell:e=>{let{row:s}=e;return!0===s.original.is_public_model_group?(0,t.jsx)(c.Z,{color:"green",size:"xs",children:"Yes"}):(0,t.jsx)(c.Z,{color:"gray",size:"xs",children:"No"})},meta:{className:"hidden md:table-cell"}},{header:"Details",id:"details",enableSorting:!1,cell:s=>{let{row:l}=s,a=l.original;return(0,t.jsxs)(o.Z,{size:"xs",variant:"secondary",onClick:()=>e(a),icon:p.Z,children:[(0,t.jsx)("span",{className:"hidden lg:inline",children:"Details"}),(0,t.jsx)("span",{className:"lg:hidden",children:"Info"})]})}}];return l?a.filter(e=>!("accessorKey"in e)||"is_public_model_group"!==e.accessorKey):a};var f=l(72162),y=l(91810),N=l(13634),_=l(42264),k=l(61994),w=l(73002),Z=l(82680),C=l(96761),S=l(12514),M=e=>{let{modelHubData:s,onFilteredDataChange:l,showFiltersCard:r=!0,className:n=""}=e,[i,c]=(0,a.useState)(""),[o,x]=(0,a.useState)(""),[m,u]=(0,a.useState)(""),[p,h]=(0,a.useState)(""),g=(0,a.useRef)([]),j=(0,a.useMemo)(()=>(null==s?void 0:s.filter(e=>{let s=e.model_group.toLowerCase().includes(i.toLowerCase()),l=""===o||e.providers.includes(o),t=""===m||e.mode===m,a=""===p||Object.entries(e).filter(e=>{let[s,l]=e;return s.startsWith("supports_")&&!0===l}).some(e=>{let[s]=e;return s.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")===p});return s&&l&&t&&a}))||[],[s,i,o,m,p]);(0,a.useEffect)(()=>{(j.length!==g.current.length||j.some((e,s)=>{var l;return e.model_group!==(null===(l=g.current[s])||void 0===l?void 0:l.model_group)}))&&(g.current=j,l(j))},[j,l]);let b=(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Z,{className:"text-sm font-medium mb-2",children:"Search Models:"}),(0,t.jsx)("input",{type:"text",placeholder:"Search model names...",value:i,onChange:e=>c(e.target.value),className:"border rounded px-3 py-2 w-64 h-10 text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Z,{className:"text-sm font-medium mb-2",children:"Provider:"}),(0,t.jsxs)("select",{value:o,onChange:e=>x(e.target.value),className:"border rounded px-3 py-2 text-sm text-gray-600 w-40 h-10",children:[(0,t.jsx)("option",{value:"",className:"text-sm text-gray-600",children:"All Providers"}),s&&(e=>{let s=new Set;return e.forEach(e=>{e.providers.forEach(e=>s.add(e))}),Array.from(s)})(s).map(e=>(0,t.jsx)("option",{value:e,className:"text-sm text-gray-800",children:e},e))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Z,{className:"text-sm font-medium mb-2",children:"Mode:"}),(0,t.jsxs)("select",{value:m,onChange:e=>u(e.target.value),className:"border rounded px-3 py-2 text-sm text-gray-600 w-32 h-10",children:[(0,t.jsx)("option",{value:"",className:"text-sm text-gray-600",children:"All Modes"}),s&&(e=>{let s=new Set;return e.forEach(e=>{e.mode&&s.add(e.mode)}),Array.from(s)})(s).map(e=>(0,t.jsx)("option",{value:e,className:"text-sm text-gray-800",children:e},e))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Z,{className:"text-sm font-medium mb-2",children:"Features:"}),(0,t.jsxs)("select",{value:p,onChange:e=>h(e.target.value),className:"border rounded px-3 py-2 text-sm text-gray-600 w-48 h-10",children:[(0,t.jsx)("option",{value:"",className:"text-sm text-gray-600",children:"All Features"}),s&&(e=>{let s=new Set;return e.forEach(e=>{Object.entries(e).filter(e=>{let[s,l]=e;return s.startsWith("supports_")&&!0===l}).forEach(e=>{let[l]=e,t=l.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");s.add(t)})}),Array.from(s).sort()})(s).map(e=>(0,t.jsx)("option",{value:e,className:"text-sm text-gray-800",children:e},e))]})]}),(i||o||m||p)&&(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)("button",{onClick:()=>{c(""),x(""),u(""),h("")},className:"text-blue-600 hover:text-blue-800 text-sm underline h-10 flex items-center",children:"Clear Filters"})})]});return r?(0,t.jsx)(S.Z,{className:"mb-6 ".concat(n),children:b}):(0,t.jsx)("div",{className:n,children:b})},P=l(9114);let{Step:L}=y.default;var A=e=>{let{visible:s,onClose:l,accessToken:r,modelHubData:i,onSuccess:o}=e,[x,m]=(0,a.useState)(0),[u,p]=(0,a.useState)(new Set),[h,g]=(0,a.useState)([]),[j,b]=(0,a.useState)(!1),[v]=N.Z.useForm(),f=()=>{m(0),p(new Set),g([]),v.resetFields(),l()},S=(e,s)=>{let l=new Set(u);s?l.add(e):l.delete(e),p(l)},A=e=>{e?p(new Set(h.map(e=>e.model_group))):p(new Set)},F=(0,a.useCallback)(e=>{g(e)},[]);(0,a.useEffect)(()=>{s&&i.length>0&&(g(i),p(new Set(i.filter(e=>!0===e.is_public_model_group).map(e=>e.model_group))))},[s,i]);let U=async()=>{if(0===u.size){P.Z.fromBackend("Please select at least one model to make public");return}b(!0);try{let e=Array.from(u);await (0,n.makeModelGroupPublic)(r,e),_.ZP.success("Successfully made ".concat(e.length," model group(s) public!")),f(),o()}catch(e){console.error("Error making model groups public:",e),P.Z.fromBackend("Failed to make model groups public. Please try again.")}finally{b(!1)}},z=()=>{let e=h.length>0&&h.every(e=>u.has(e.model_group)),s=u.size>0&&!e;return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(C.Z,{children:"Select Models to Make Public"}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(k.Z,{checked:e,indeterminate:s,onChange:e=>A(e.target.checked),disabled:0===h.length,children:["Select All ",h.length>0&&"(".concat(h.length,")")]})})]}),(0,t.jsx)(d.Z,{className:"text-sm text-gray-600",children:"Select the models you want to be visible on the public model hub. Users will still require a valid API key to use these models."}),(0,t.jsx)(M,{modelHubData:i,onFilteredDataChange:F,showFiltersCard:!1,className:"border rounded-lg p-4 bg-gray-50"}),(0,t.jsx)("div",{className:"max-h-96 overflow-y-auto border rounded-lg p-4",children:(0,t.jsx)("div",{className:"space-y-3",children:0===h.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,t.jsx)(d.Z,{children:"No models match the current filters."})}):h.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50",children:[(0,t.jsx)(k.Z,{checked:u.has(e.model_group),onChange:s=>S(e.model_group,s.target.checked)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.Z,{className:"font-medium",children:e.model_group}),e.mode&&(0,t.jsx)(c.Z,{color:"green",size:"sm",children:e.mode})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.providers.map(e=>(0,t.jsx)(c.Z,{color:"blue",size:"xs",children:e},e))})]})]},e.model_group))})}),u.size>0&&(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,t.jsxs)(d.Z,{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:u.size})," model",1!==u.size?"s":""," selected"]})})]})},E=()=>(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(C.Z,{children:"Confirm Making Models Public"}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)(d.Z,{className:"text-sm text-yellow-800",children:[(0,t.jsx)("strong",{children:"Warning:"})," Once you make these models public, anyone who can go to the ",(0,t.jsx)("code",{children:"/ui/model_hub_table"})," will be able to know they exist on the proxy."]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(d.Z,{className:"font-medium",children:"Models to be made public:"}),(0,t.jsx)("div",{className:"max-h-48 overflow-y-auto border rounded-lg p-3",children:(0,t.jsx)("div",{className:"space-y-2",children:Array.from(u).map(e=>{let s=i.find(s=>s.model_group===e);return(0,t.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Z,{className:"font-medium",children:e}),s&&(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:s.providers.map(e=>(0,t.jsx)(c.Z,{color:"blue",size:"xs",children:e},e))})]})},e)})})})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,t.jsxs)(d.Z,{className:"text-sm text-blue-800",children:["Total: ",(0,t.jsx)("strong",{children:u.size})," model",1!==u.size?"s":""," will be made public"]})})]});return(0,t.jsx)(Z.Z,{title:"Make Models Public",open:s,onCancel:f,footer:null,width:1200,maskClosable:!1,children:(0,t.jsxs)(N.Z,{form:v,layout:"vertical",children:[(0,t.jsxs)(y.default,{current:x,className:"mb-6",children:[(0,t.jsx)(L,{title:"Select Models"}),(0,t.jsx)(L,{title:"Confirm"})]}),(()=>{switch(x){case 0:return z();case 1:return E();default:return null}})(),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsx)(w.ZP,{onClick:0===x?f:()=>{1===x&&m(0)},children:0===x?"Cancel":"Previous"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[0===x&&(0,t.jsx)(w.ZP,{onClick:()=>{if(0===x){if(0===u.size){P.Z.fromBackend("Please select at least one model to make public");return}m(1)}},disabled:0===u.size,children:"Next"}),1===x&&(0,t.jsx)(w.ZP,{onClick:U,loading:j,children:"Make Public"})]})]})]})})},F=l(69870),U=l(57018),z=l(17906),E=l(78867),D=l(20347),R=e=>{var s,l;let{accessToken:c,publicPage:o,premiumUser:d,userRole:x}=e,[m,u]=(0,a.useState)(!1),[p,h]=(0,a.useState)(null),[g,j]=(0,a.useState)(!0),[b,y]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),[w,C]=(0,a.useState)(null),[S,P]=(0,a.useState)([]),[L,R]=(0,a.useState)(!1),H=(0,r.useRouter)(),O=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=async e=>{try{j(!0);let s=await (0,n.modelHubCall)(e);console.log("ModelHubData:",s),h(s.data),(0,n.getConfigFieldSetting)(e,"enable_public_model_hub").then(e=>{console.log("data: ".concat(JSON.stringify(e))),!0==e.field_value&&u(!0)}).catch(e=>{})}catch(e){console.error("There was an error fetching the model data",e)}finally{j(!1)}},s=async()=>{try{var e,s;j(!0);let l=await (0,n.modelHubPublicModelsCall)();console.log("ModelHubData:",l),console.log("First model structure:",l[0]),console.log("Model has model_group?",null===(e=l[0])||void 0===e?void 0:e.model_group),console.log("Model has providers?",null===(s=l[0])||void 0===s?void 0:s.providers),h(l),u(!0)}catch(e){console.error("There was an error fetching the public model data",e)}finally{j(!1)}};c?e(c):o&&s()},[c,o]);let T=()=>{c&&R(!0)},B=()=>{y(!1),k(!1),C(null)},I=()=>{y(!1),k(!1),C(null)},K=e=>{navigator.clipboard.writeText(e),_.ZP.success("Copied to clipboard!")},Y=e=>e.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),W=e=>Object.entries(e).filter(e=>{let[s,l]=e;return s.startsWith("supports_")&&!0===l}).map(e=>{let[s]=e;return s}),V=e=>"$".concat((1e6*e).toFixed(2)),q=(0,a.useCallback)(e=>{P(e)},[]);return(console.log("publicPage: ",o),console.log("publicPageAllowed: ",m),o&&m)?(0,t.jsx)(f.Z,{accessToken:c}):(0,t.jsxs)("div",{className:"w-full mx-4 h-[75vh]",children:[!1==o?(0,t.jsxs)("div",{className:"w-full m-2 mt-2 p-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-start",children:[(0,t.jsx)(U.Dx,{className:"text-center",children:"Model Hub"}),(0,D.tY)(x||"")?(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Make models public for developers to know what models are available on the proxy."}):(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"A list of all public model names personally available to you."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(U.xv,{children:"Model Hub URL:"}),(0,t.jsxs)("div",{className:"flex items-center bg-gray-200 px-2 py-1 rounded",children:[(0,t.jsx)(U.xv,{className:"mr-2",children:"".concat((0,n.getProxyBaseUrl)(),"/ui/model_hub_table")}),(0,t.jsx)("button",{onClick:()=>K("".concat((0,n.getProxyBaseUrl)(),"/ui/model_hub_table")),className:"p-1 hover:bg-gray-300 rounded transition-colors",title:"Copy URL",children:(0,t.jsx)(E.Z,{size:16,className:"text-gray-600"})})]}),!1==o&&(0,D.tY)(x||"")&&(0,t.jsx)(U.zx,{className:"ml-4",onClick:()=>T(),children:"Make Public"})]})]}),(0,D.tY)(x||"")&&(0,t.jsx)("div",{className:"mt-8 mb-2",children:(0,t.jsx)(F.Z,{accessToken:c,userRole:x})}),(0,t.jsxs)(U.Zb,{children:[(0,t.jsx)(M,{modelHubData:p||[],onFilteredDataChange:q}),(0,t.jsx)(i.C,{columns:v(e=>{C(e),y(!0)},K,o),data:S,isLoading:g,table:O,defaultSorting:[{id:"model_group",desc:!1}]})]}),(0,t.jsx)("div",{className:"mt-4 text-center space-y-2",children:(0,t.jsxs)(U.xv,{className:"text-sm text-gray-600",children:["Showing ",S.length," of ",(null==p?void 0:p.length)||0," models"]})})]}):(0,t.jsxs)(U.Zb,{className:"mx-auto max-w-xl mt-10",children:[(0,t.jsx)(U.xv,{className:"text-xl text-center mb-2 text-black",children:"Public Model Hub not enabled."}),(0,t.jsx)("p",{className:"text-base text-center text-slate-800",children:"Ask your proxy admin to enable this on their Admin UI."})]}),(0,t.jsx)(Z.Z,{title:"Public Model Hub",width:600,visible:N,footer:null,onOk:B,onCancel:I,children:(0,t.jsxs)("div",{className:"pt-5 pb-5",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,t.jsx)(U.xv,{className:"text-base mr-2",children:"Shareable Link:"}),(0,t.jsx)(U.xv,{className:"max-w-sm ml-2 bg-gray-200 pr-2 pl-2 pt-1 pb-1 text-center rounded",children:"".concat((0,n.getProxyBaseUrl)(),"/ui/model_hub_table")})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(U.zx,{onClick:()=>{H.replace("/model_hub_table?key=".concat(c))},children:"See Page"})})]})}),(0,t.jsx)(Z.Z,{title:(null==w?void 0:w.model_group)||"Model Details",width:1e3,visible:b,footer:null,onOk:B,onCancel:I,children:w&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Model Overview"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Model Group:"}),(0,t.jsx)(U.xv,{children:w.model_group})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Mode:"}),(0,t.jsx)(U.xv,{children:w.mode||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Providers:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:w.providers.map(e=>(0,t.jsx)(U.Ct,{color:"blue",children:e},e))})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Token & Cost Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Max Input Tokens:"}),(0,t.jsx)(U.xv,{children:(null===(s=w.max_input_tokens)||void 0===s?void 0:s.toLocaleString())||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Max Output Tokens:"}),(0,t.jsx)(U.xv,{children:(null===(l=w.max_output_tokens)||void 0===l?void 0:l.toLocaleString())||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Input Cost per 1M Tokens:"}),(0,t.jsx)(U.xv,{children:w.input_cost_per_token?V(w.input_cost_per_token):"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Output Cost per 1M Tokens:"}),(0,t.jsx)(U.xv,{children:w.output_cost_per_token?V(w.output_cost_per_token):"Not specified"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Capabilities"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let e=W(w),s=["green","blue","purple","orange","red","yellow"];return 0===e.length?(0,t.jsx)(U.xv,{className:"text-gray-500",children:"No special capabilities listed"}):e.map((e,l)=>(0,t.jsx)(U.Ct,{color:s[l%s.length],children:Y(e)},e))})()})]}),(w.tpm||w.rpm)&&(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Rate Limits"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[w.tpm&&(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Tokens per Minute:"}),(0,t.jsx)(U.xv,{children:w.tpm.toLocaleString()})]}),w.rpm&&(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"font-medium",children:"Requests per Minute:"}),(0,t.jsx)(U.xv,{children:w.rpm.toLocaleString()})]})]})]}),w.supported_openai_params&&(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Supported OpenAI Parameters"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:w.supported_openai_params.map(e=>(0,t.jsx)(U.Ct,{color:"green",children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(U.xv,{className:"text-lg font-semibold mb-4",children:"Usage Example"}),(0,t.jsx)(z.Z,{language:"python",className:"text-sm",children:'import openai\n\nclient = openai.OpenAI(\n    api_key="your_api_key",\n    base_url="http://0.0.0.0:4000"  # Your LiteLLM Proxy URL\n)\n\nresponse = client.chat.completions.create(\n    model="'.concat(w.model_group,'",\n    messages=[\n        {\n            "role": "user",\n            "content": "Hello, how are you?"\n        }\n    ]\n)\n\nprint(response.choices[0].message.content)')})]})]})}),(0,t.jsx)(A,{visible:L,onClose:()=>R(!1),accessToken:c||"",modelHubData:p||[],onSuccess:()=>{c&&(async()=>{try{let e=await (0,n.modelHubCall)(c);h(e.data)}catch(e){console.error("Error refreshing model data:",e)}})()}})]})}},69870:function(e,s,l){var t=l(57437),a=l(2265),r=l(82680),n=l(42264),i=l(86462),c=l(47686),o=l(77355),d=l(93416),x=l(74998),m=l(20347),u=l(19250),p=l(95704),h=l(9114);s.Z=e=>{let{accessToken:s,userRole:l}=e,[g,j]=(0,a.useState)([]),[b,v]=(0,a.useState)({url:"",displayName:""}),[f,y]=(0,a.useState)(null),[N,_]=(0,a.useState)(!1),[k,w]=(0,a.useState)(!0),Z=async()=>{if(s)try{_(!0);let e=await (0,u.getPublicModelHubInfo)();if(e&&e.useful_links){let s=e.useful_links||{},l=Object.entries(s).map((e,s)=>{let[l,t]=e;return{id:"".concat(s,"-").concat(l),displayName:l,url:t}});j(l)}else j([])}catch(e){console.error("Error fetching useful links:",e),j([])}finally{_(!1)}};if((0,a.useEffect)(()=>{Z()},[s]),!(0,m.tY)(l||""))return null;let C=async e=>{if(!s)return!1;try{let l={};return e.forEach(e=>{l[e.displayName]=e.url}),await (0,u.updateUsefulLinksCall)(s,l),r.Z.success({title:"Links Saved Successfully",content:(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Your useful links have been saved and are now visible on the public model hub."}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("p",{className:"text-sm text-blue-800 mb-2 font-medium",children:"View your updated model hub:"}),(0,t.jsx)("a",{href:"".concat((0,u.getProxyBaseUrl)(),"/ui/model_hub_table"),target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 underline text-sm font-medium",children:"Open Public Model Hub →"})]})]}),width:500,okText:"Close",maskClosable:!0,keyboard:!0}),!0}catch(e){return console.error("Error saving links:",e),h.Z.fromBackend("Failed to save links - ".concat(e)),!1}},S=async()=>{if(!b.url||!b.displayName)return;try{new URL(b.url)}catch(e){h.Z.fromBackend("Please enter a valid URL");return}if(g.some(e=>e.displayName===b.displayName)){h.Z.fromBackend("A link with this display name already exists");return}let e=[...g,{id:"".concat(Date.now(),"-").concat(b.displayName),displayName:b.displayName,url:b.url}];await C(e)&&(j(e),v({url:"",displayName:""}),n.ZP.success("Link added successfully"))},M=e=>{y({...e})},P=async()=>{if(!f)return;try{new URL(f.url)}catch(e){h.Z.fromBackend("Please enter a valid URL");return}if(g.some(e=>e.id!==f.id&&e.displayName===f.displayName)){h.Z.fromBackend("A link with this display name already exists");return}let e=g.map(e=>e.id===f.id?f:e);await C(e)&&(j(e),y(null),n.ZP.success("Link updated successfully"))},L=()=>{y(null)},A=async e=>{let s=g.filter(s=>s.id!==e);await C(s)&&(j(s),n.ZP.success("Link deleted successfully"))},F=e=>{window.open(e,"_blank")};return(0,t.jsxs)(p.Zb,{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>w(!k),children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)(p.Dx,{className:"mb-0",children:"Link Management"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Manage the links that are displayed under 'Useful Links' on the public model hub."})]}),(0,t.jsx)("div",{className:"flex items-center",children:k?(0,t.jsx)(i.Z,{className:"w-5 h-5 text-gray-500"}):(0,t.jsx)(c.Z,{className:"w-5 h-5 text-gray-500"})})]}),k&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)(p.xv,{className:"text-sm font-medium text-gray-700 mb-2",children:"Add New Link"}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"URL"}),(0,t.jsx)("input",{type:"text",value:b.url,onChange:e=>v({...b,url:e.target.value}),placeholder:"https://example.com",className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Display Name"}),(0,t.jsx)("input",{type:"text",value:b.displayName,onChange:e=>v({...b,displayName:e.target.value}),placeholder:"Friendly name",className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm"})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsxs)("button",{onClick:S,disabled:!b.url||!b.displayName,className:"flex items-center px-4 py-2 rounded-md text-sm ".concat(b.url&&b.displayName?"bg-green-600 text-white hover:bg-green-700":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:[(0,t.jsx)(o.Z,{className:"w-4 h-4 mr-1"}),"Add Link"]})})]})]}),(0,t.jsx)(p.xv,{className:"text-sm font-medium text-gray-700 mb-2",children:"Manage Existing Links"}),(0,t.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(p.iA,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,t.jsx)(p.ss,{children:(0,t.jsxs)(p.SC,{children:[(0,t.jsx)(p.xs,{className:"py-1 h-8",children:"Display Name"}),(0,t.jsx)(p.xs,{className:"py-1 h-8",children:"URL"}),(0,t.jsx)(p.xs,{className:"py-1 h-8",children:"Actions"})]})}),(0,t.jsxs)(p.RM,{children:[g.map(e=>(0,t.jsx)(p.SC,{className:"h-8",children:f&&f.id===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.pj,{className:"py-0.5",children:(0,t.jsx)("input",{type:"text",value:f.displayName,onChange:e=>y({...f,displayName:e.target.value}),className:"w-full px-2 py-1 border border-gray-300 rounded-md text-sm"})}),(0,t.jsx)(p.pj,{className:"py-0.5",children:(0,t.jsx)("input",{type:"text",value:f.url,onChange:e=>y({...f,url:e.target.value}),className:"w-full px-2 py-1 border border-gray-300 rounded-md text-sm"})}),(0,t.jsx)(p.pj,{className:"py-0.5 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:P,className:"text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded hover:bg-blue-100",children:"Save"}),(0,t.jsx)("button",{onClick:L,className:"text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded hover:bg-gray-100",children:"Cancel"})]})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.pj,{className:"py-0.5 text-sm text-gray-900",children:e.displayName}),(0,t.jsx)(p.pj,{className:"py-0.5 text-sm text-gray-500",children:e.url}),(0,t.jsx)(p.pj,{className:"py-0.5 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>F(e.url),className:"text-xs bg-green-50 text-green-600 px-2 py-1 rounded hover:bg-green-100",children:"Use"}),(0,t.jsx)("button",{onClick:()=>M(e),className:"text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded hover:bg-blue-100",children:(0,t.jsx)(d.Z,{className:"w-3 h-3"})}),(0,t.jsx)("button",{onClick:()=>A(e.id),className:"text-xs bg-red-50 text-red-600 px-2 py-1 rounded hover:bg-red-100",children:(0,t.jsx)(x.Z,{className:"w-3 h-3"})})]})})]})},e.id)),0===g.length&&(0,t.jsx)(p.SC,{children:(0,t.jsx)(p.pj,{colSpan:3,className:"py-0.5 text-sm text-gray-500 text-center",children:"No links added yet. Add a new link above."})})]})]})})})]})]})}},20347:function(e,s,l){l.d(s,{LQ:function(){return r},ZL:function(){return t},lo:function(){return a},tY:function(){return n}});let t=["Admin","Admin Viewer","proxy_admin","proxy_admin_viewer","org_admin"],a=["Internal User","Internal Viewer"],r=["Internal User","Admin"],n=e=>t.includes(e)}}]);