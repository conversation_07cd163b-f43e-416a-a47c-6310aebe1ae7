model_list:
 - model_name: fake-openai-endpoint
   litellm_params:
    model: openai/fake
    api_key: fake-key
    api_base: https://webhook.site/4feb0d46-4b23-468c-bf55-7008b5deb36d
 - model_name: gpt-5-mini
   litellm_params:
    model: azure/gpt-5-mini
    api_base: os.environ/AZURE_GPT_5_MINI_API_BASE # runs os.getenv("AZURE_API_BASE")
    api_key: os.environ/AZURE_GPT_5_MINI_API_KEY # runs os.getenv("AZURE_API_KEY")
    stream_timeout: 60
    merge_reasoning_content_in_choices: true
   model_info:
    mode: chat

litellm_settings:
  cache: true
  cache_params:
    type: redis
    ttl: 600
    supported_call_types: ["acompletion", "completion"]

  model_group_settings:
    forward_client_headers_to_llm_api:
      - fake-openai-endpoint
