"""
Nothing here but dictionaries for generating LinearSegmentedColormaps,
and a dictionary of these dictionaries.

Documentation for each is in pyplot.colormaps().  Please update this
with the purpose and type of your colormap if you add data for one here.
"""

from functools import partial

import numpy as np

_binary_data = {
    'red':    ((0., 1., 1.), (1., 0., 0.)),
    'green':  ((0., 1., 1.), (1., 0., 0.)),
    'blue':   ((0., 1., 1.), (1., 0., 0.))
    }

_autumn_data = {'red':   ((0., 1.0, 1.0), (1.0, 1.0, 1.0)),
                'green': ((0., 0., 0.), (1.0, 1.0, 1.0)),
                'blue':  ((0., 0., 0.), (1.0, 0., 0.))}

_bone_data = {'red':   ((0., 0., 0.),
                        (0.746032, 0.652778, 0.652778),
                        (1.0, 1.0, 1.0)),
              'green': ((0., 0., 0.),
                        (0.365079, 0.319444, 0.319444),
                        (0.746032, 0.777778, 0.777778),
                        (1.0, 1.0, 1.0)),
              'blue':  ((0., 0., 0.),
                        (0.365079, 0.444444, 0.444444),
                        (1.0, 1.0, 1.0))}

_cool_data = {'red':   ((0., 0., 0.), (1.0, 1.0, 1.0)),
              'green': ((0., 1., 1.), (1.0, 0.,  0.)),
              'blue':  ((0., 1., 1.), (1.0, 1.,  1.))}

_copper_data = {'red':   ((0., 0., 0.),
                          (0.809524, 1.000000, 1.000000),
                          (1.0, 1.0, 1.0)),
                'green': ((0., 0., 0.),
                          (1.0, 0.7812, 0.7812)),
                'blue':  ((0., 0., 0.),
                          (1.0, 0.4975, 0.4975))}

def _flag_red(x): return 0.75 * np.sin((x * 31.5 + 0.25) * np.pi) + 0.5
def _flag_green(x): return np.sin(x * 31.5 * np.pi)
def _flag_blue(x): return 0.75 * np.sin((x * 31.5 - 0.25) * np.pi) + 0.5
_flag_data = {'red': _flag_red, 'green': _flag_green, 'blue': _flag_blue}

def _prism_red(x): return 0.75 * np.sin((x * 20.9 + 0.25) * np.pi) + 0.67
def _prism_green(x): return 0.75 * np.sin((x * 20.9 - 0.25) * np.pi) + 0.33
def _prism_blue(x): return -1.1 * np.sin((x * 20.9) * np.pi)
_prism_data = {'red': _prism_red, 'green': _prism_green, 'blue': _prism_blue}

def _ch_helper(gamma, s, r, h, p0, p1, x):
    """Helper function for generating picklable cubehelix colormaps."""
    # Apply gamma factor to emphasise low or high intensity values
    xg = x ** gamma
    # Calculate amplitude and angle of deviation from the black to white
    # diagonal in the plane of constant perceived intensity.
    a = h * xg * (1 - xg) / 2
    phi = 2 * np.pi * (s / 3 + r * x)
    return xg + a * (p0 * np.cos(phi) + p1 * np.sin(phi))

def cubehelix(gamma=1.0, s=0.5, r=-1.5, h=1.0):
    """
    Return custom data dictionary of (r, g, b) conversion functions, which can
    be used with `.ColormapRegistry.register`, for the cubehelix color scheme.

    Unlike most other color schemes cubehelix was designed by D.A. Green to
    be monotonically increasing in terms of perceived brightness.
    Also, when printed on a black and white postscript printer, the scheme
    results in a greyscale with monotonically increasing brightness.
    This color scheme is named cubehelix because the (r, g, b) values produced
    can be visualised as a squashed helix around the diagonal in the
    (r, g, b) color cube.

    For a unit color cube (i.e. 3D coordinates for (r, g, b) each in the
    range 0 to 1) the color scheme starts at (r, g, b) = (0, 0, 0), i.e. black,
    and finishes at (r, g, b) = (1, 1, 1), i.e. white. For some fraction *x*,
    between 0 and 1, the color is the corresponding grey value at that
    fraction along the black to white diagonal (x, x, x) plus a color
    element. This color element is calculated in a plane of constant
    perceived intensity and controlled by the following parameters.

    Parameters
    ----------
    gamma : float, default: 1
        Gamma factor emphasizing either low intensity values (gamma < 1), or
        high intensity values (gamma > 1).
    s : float, default: 0.5 (purple)
        The starting color.
    r : float, default: -1.5
        The number of r, g, b rotations in color that are made from the start
        to the end of the color scheme.  The default of -1.5 corresponds to ->
        B -> G -> R -> B.
    h : float, default: 1
        The hue, i.e. how saturated the colors are. If this parameter is zero
        then the color scheme is purely a greyscale.
    """
    return {'red': partial(_ch_helper, gamma, s, r, h, -0.14861, 1.78277),
            'green': partial(_ch_helper, gamma, s, r, h, -0.29227, -0.90649),
            'blue': partial(_ch_helper, gamma, s, r, h, 1.97294, 0.0)}

_cubehelix_data = cubehelix()

_bwr_data = ((0.0, 0.0, 1.0), (1.0, 1.0, 1.0), (1.0, 0.0, 0.0))
_brg_data = ((0.0, 0.0, 1.0), (1.0, 0.0, 0.0), (0.0, 1.0, 0.0))

# Gnuplot palette functions
def _g0(x): return 0
def _g1(x): return 0.5
def _g2(x): return 1
def _g3(x): return x
def _g4(x): return x ** 2
def _g5(x): return x ** 3
def _g6(x): return x ** 4
def _g7(x): return np.sqrt(x)
def _g8(x): return np.sqrt(np.sqrt(x))
def _g9(x): return np.sin(x * np.pi / 2)
def _g10(x): return np.cos(x * np.pi / 2)
def _g11(x): return np.abs(x - 0.5)
def _g12(x): return (2 * x - 1) ** 2
def _g13(x): return np.sin(x * np.pi)
def _g14(x): return np.abs(np.cos(x * np.pi))
def _g15(x): return np.sin(x * 2 * np.pi)
def _g16(x): return np.cos(x * 2 * np.pi)
def _g17(x): return np.abs(np.sin(x * 2 * np.pi))
def _g18(x): return np.abs(np.cos(x * 2 * np.pi))
def _g19(x): return np.abs(np.sin(x * 4 * np.pi))
def _g20(x): return np.abs(np.cos(x * 4 * np.pi))
def _g21(x): return 3 * x
def _g22(x): return 3 * x - 1
def _g23(x): return 3 * x - 2
def _g24(x): return np.abs(3 * x - 1)
def _g25(x): return np.abs(3 * x - 2)
def _g26(x): return (3 * x - 1) / 2
def _g27(x): return (3 * x - 2) / 2
def _g28(x): return np.abs((3 * x - 1) / 2)
def _g29(x): return np.abs((3 * x - 2) / 2)
def _g30(x): return x / 0.32 - 0.78125
def _g31(x): return 2 * x - 0.84
def _g32(x):
    ret = np.zeros(len(x))
    m = (x < 0.25)
    ret[m] = 4 * x[m]
    m = (x >= 0.25) & (x < 0.92)
    ret[m] = -2 * x[m] + 1.84
    m = (x >= 0.92)
    ret[m] = x[m] / 0.08 - 11.5
    return ret
def _g33(x): return np.abs(2 * x - 0.5)
def _g34(x): return 2 * x
def _g35(x): return 2 * x - 0.5
def _g36(x): return 2 * x - 1

gfunc = {i: globals()[f"_g{i}"] for i in range(37)}

_gnuplot_data = {
        'red': gfunc[7],
        'green': gfunc[5],
        'blue': gfunc[15],
}

_gnuplot2_data = {
        'red': gfunc[30],
        'green': gfunc[31],
        'blue': gfunc[32],
}

_ocean_data = {
        'red': gfunc[23],
        'green': gfunc[28],
        'blue': gfunc[3],
}

_afmhot_data = {
        'red': gfunc[34],
        'green': gfunc[35],
        'blue': gfunc[36],
}

_rainbow_data = {
        'red': gfunc[33],
        'green': gfunc[13],
        'blue': gfunc[10],
}

_seismic_data = (
        (0.0, 0.0, 0.3), (0.0, 0.0, 1.0),
        (1.0, 1.0, 1.0), (1.0, 0.0, 0.0),
        (0.5, 0.0, 0.0))

_terrain_data = (
        (0.00, (0.2, 0.2, 0.6)),
        (0.15, (0.0, 0.6, 1.0)),
        (0.25, (0.0, 0.8, 0.4)),
        (0.50, (1.0, 1.0, 0.6)),
        (0.75, (0.5, 0.36, 0.33)),
        (1.00, (1.0, 1.0, 1.0)))

_gray_data = {'red':   ((0., 0, 0), (1., 1, 1)),
              'green': ((0., 0, 0), (1., 1, 1)),
              'blue':  ((0., 0, 0), (1., 1, 1))}

_hot_data = {'red':   ((0., 0.0416, 0.0416),
                       (0.365079, 1.000000, 1.000000),
                       (1.0, 1.0, 1.0)),
             'green': ((0., 0., 0.),
                       (0.365079, 0.000000, 0.000000),
                       (0.746032, 1.000000, 1.000000),
                       (1.0, 1.0, 1.0)),
             'blue':  ((0., 0., 0.),
                       (0.746032, 0.000000, 0.000000),
                       (1.0, 1.0, 1.0))}

_hsv_data = {'red':   ((0., 1., 1.),
                       (0.158730, 1.000000, 1.000000),
                       (0.174603, 0.968750, 0.968750),
                       (0.333333, 0.031250, 0.031250),
                       (0.349206, 0.000000, 0.000000),
                       (0.666667, 0.000000, 0.000000),
                       (0.682540, 0.031250, 0.031250),
                       (0.841270, 0.968750, 0.968750),
                       (0.857143, 1.000000, 1.000000),
                       (1.0, 1.0, 1.0)),
             'green': ((0., 0., 0.),
                       (0.158730, 0.937500, 0.937500),
                       (0.174603, 1.000000, 1.000000),
                       (0.507937, 1.000000, 1.000000),
                       (0.666667, 0.062500, 0.062500),
                       (0.682540, 0.000000, 0.000000),
                       (1.0, 0., 0.)),
             'blue':  ((0., 0., 0.),
                       (0.333333, 0.000000, 0.000000),
                       (0.349206, 0.062500, 0.062500),
                       (0.507937, 1.000000, 1.000000),
                       (0.841270, 1.000000, 1.000000),
                       (0.857143, 0.937500, 0.937500),
                       (1.0, 0.09375, 0.09375))}

_jet_data = {'red':   ((0.00, 0, 0),
                       (0.35, 0, 0),
                       (0.66, 1, 1),
                       (0.89, 1, 1),
                       (1.00, 0.5, 0.5)),
             'green': ((0.000, 0, 0),
                       (0.125, 0, 0),
                       (0.375, 1, 1),
                       (0.640, 1, 1),
                       (0.910, 0, 0),
                       (1.000, 0, 0)),
             'blue':  ((0.00, 0.5, 0.5),
                       (0.11, 1, 1),
                       (0.34, 1, 1),
                       (0.65, 0, 0),
                       (1.00, 0, 0))}

_pink_data = {'red':   ((0., 0.1178, 0.1178), (0.015873, 0.195857, 0.195857),
                        (0.031746, 0.250661, 0.250661),
                        (0.047619, 0.295468, 0.295468),
                        (0.063492, 0.334324, 0.334324),
                        (0.079365, 0.369112, 0.369112),
                        (0.095238, 0.400892, 0.400892),
                        (0.111111, 0.430331, 0.430331),
                        (0.126984, 0.457882, 0.457882),
                        (0.142857, 0.483867, 0.483867),
                        (0.158730, 0.508525, 0.508525),
                        (0.174603, 0.532042, 0.532042),
                        (0.190476, 0.554563, 0.554563),
                        (0.206349, 0.576204, 0.576204),
                        (0.222222, 0.597061, 0.597061),
                        (0.238095, 0.617213, 0.617213),
                        (0.253968, 0.636729, 0.636729),
                        (0.269841, 0.655663, 0.655663),
                        (0.285714, 0.674066, 0.674066),
                        (0.301587, 0.691980, 0.691980),
                        (0.317460, 0.709441, 0.709441),
                        (0.333333, 0.726483, 0.726483),
                        (0.349206, 0.743134, 0.743134),
                        (0.365079, 0.759421, 0.759421),
                        (0.380952, 0.766356, 0.766356),
                        (0.396825, 0.773229, 0.773229),
                        (0.412698, 0.780042, 0.780042),
                        (0.428571, 0.786796, 0.786796),
                        (0.444444, 0.793492, 0.793492),
                        (0.460317, 0.800132, 0.800132),
                        (0.476190, 0.806718, 0.806718),
                        (0.492063, 0.813250, 0.813250),
                        (0.507937, 0.819730, 0.819730),
                        (0.523810, 0.826160, 0.826160),
                        (0.539683, 0.832539, 0.832539),
                        (0.555556, 0.838870, 0.838870),
                        (0.571429, 0.845154, 0.845154),
                        (0.587302, 0.851392, 0.851392),
                        (0.603175, 0.857584, 0.857584),
                        (0.619048, 0.863731, 0.863731),
                        (0.634921, 0.869835, 0.869835),
                        (0.650794, 0.875897, 0.875897),
                        (0.666667, 0.881917, 0.881917),
                        (0.682540, 0.887896, 0.887896),
                        (0.698413, 0.893835, 0.893835),
                        (0.714286, 0.899735, 0.899735),
                        (0.730159, 0.905597, 0.905597),
                        (0.746032, 0.911421, 0.911421),
                        (0.761905, 0.917208, 0.917208),
                        (0.777778, 0.922958, 0.922958),
                        (0.793651, 0.928673, 0.928673),
                        (0.809524, 0.934353, 0.934353),
                        (0.825397, 0.939999, 0.939999),
                        (0.841270, 0.945611, 0.945611),
                        (0.857143, 0.951190, 0.951190),
                        (0.873016, 0.956736, 0.956736),
                        (0.888889, 0.962250, 0.962250),
                        (0.904762, 0.967733, 0.967733),
                        (0.920635, 0.973185, 0.973185),
                        (0.936508, 0.978607, 0.978607),
                        (0.952381, 0.983999, 0.983999),
                        (0.968254, 0.989361, 0.989361),
                        (0.984127, 0.994695, 0.994695), (1.0, 1.0, 1.0)),
              'green': ((0., 0., 0.), (0.015873, 0.102869, 0.102869),
                        (0.031746, 0.145479, 0.145479),
                        (0.047619, 0.178174, 0.178174),
                        (0.063492, 0.205738, 0.205738),
                        (0.079365, 0.230022, 0.230022),
                        (0.095238, 0.251976, 0.251976),
                        (0.111111, 0.272166, 0.272166),
                        (0.126984, 0.290957, 0.290957),
                        (0.142857, 0.308607, 0.308607),
                        (0.158730, 0.325300, 0.325300),
                        (0.174603, 0.341178, 0.341178),
                        (0.190476, 0.356348, 0.356348),
                        (0.206349, 0.370899, 0.370899),
                        (0.222222, 0.384900, 0.384900),
                        (0.238095, 0.398410, 0.398410),
                        (0.253968, 0.411476, 0.411476),
                        (0.269841, 0.424139, 0.424139),
                        (0.285714, 0.436436, 0.436436),
                        (0.301587, 0.448395, 0.448395),
                        (0.317460, 0.460044, 0.460044),
                        (0.333333, 0.471405, 0.471405),
                        (0.349206, 0.482498, 0.482498),
                        (0.365079, 0.493342, 0.493342),
                        (0.380952, 0.517549, 0.517549),
                        (0.396825, 0.540674, 0.540674),
                        (0.412698, 0.562849, 0.562849),
                        (0.428571, 0.584183, 0.584183),
                        (0.444444, 0.604765, 0.604765),
                        (0.460317, 0.624669, 0.624669),
                        (0.476190, 0.643958, 0.643958),
                        (0.492063, 0.662687, 0.662687),
                        (0.507937, 0.680900, 0.680900),
                        (0.523810, 0.698638, 0.698638),
                        (0.539683, 0.715937, 0.715937),
                        (0.555556, 0.732828, 0.732828),
                        (0.571429, 0.749338, 0.749338),
                        (0.587302, 0.765493, 0.765493),
                        (0.603175, 0.781313, 0.781313),
                        (0.619048, 0.796819, 0.796819),
                        (0.634921, 0.812029, 0.812029),
                        (0.650794, 0.826960, 0.826960),
                        (0.666667, 0.841625, 0.841625),
                        (0.682540, 0.856040, 0.856040),
                        (0.698413, 0.870216, 0.870216),
                        (0.714286, 0.884164, 0.884164),
                        (0.730159, 0.897896, 0.897896),
                        (0.746032, 0.911421, 0.911421),
                        (0.761905, 0.917208, 0.917208),
                        (0.777778, 0.922958, 0.922958),
                        (0.793651, 0.928673, 0.928673),
                        (0.809524, 0.934353, 0.934353),
                        (0.825397, 0.939999, 0.939999),
                        (0.841270, 0.945611, 0.945611),
                        (0.857143, 0.951190, 0.951190),
                        (0.873016, 0.956736, 0.956736),
                        (0.888889, 0.962250, 0.962250),
                        (0.904762, 0.967733, 0.967733),
                        (0.920635, 0.973185, 0.973185),
                        (0.936508, 0.978607, 0.978607),
                        (0.952381, 0.983999, 0.983999),
                        (0.968254, 0.989361, 0.989361),
                        (0.984127, 0.994695, 0.994695), (1.0, 1.0, 1.0)),
              'blue':  ((0., 0., 0.), (0.015873, 0.102869, 0.102869),
                        (0.031746, 0.145479, 0.145479),
                        (0.047619, 0.178174, 0.178174),
                        (0.063492, 0.205738, 0.205738),
                        (0.079365, 0.230022, 0.230022),
                        (0.095238, 0.251976, 0.251976),
                        (0.111111, 0.272166, 0.272166),
                        (0.126984, 0.290957, 0.290957),
                        (0.142857, 0.308607, 0.308607),
                        (0.158730, 0.325300, 0.325300),
                        (0.174603, 0.341178, 0.341178),
                        (0.190476, 0.356348, 0.356348),
                        (0.206349, 0.370899, 0.370899),
                        (0.222222, 0.384900, 0.384900),
                        (0.238095, 0.398410, 0.398410),
                        (0.253968, 0.411476, 0.411476),
                        (0.269841, 0.424139, 0.424139),
                        (0.285714, 0.436436, 0.436436),
                        (0.301587, 0.448395, 0.448395),
                        (0.317460, 0.460044, 0.460044),
                        (0.333333, 0.471405, 0.471405),
                        (0.349206, 0.482498, 0.482498),
                        (0.365079, 0.493342, 0.493342),
                        (0.380952, 0.503953, 0.503953),
                        (0.396825, 0.514344, 0.514344),
                        (0.412698, 0.524531, 0.524531),
                        (0.428571, 0.534522, 0.534522),
                        (0.444444, 0.544331, 0.544331),
                        (0.460317, 0.553966, 0.553966),
                        (0.476190, 0.563436, 0.563436),
                        (0.492063, 0.572750, 0.572750),
                        (0.507937, 0.581914, 0.581914),
                        (0.523810, 0.590937, 0.590937),
                        (0.539683, 0.599824, 0.599824),
                        (0.555556, 0.608581, 0.608581),
                        (0.571429, 0.617213, 0.617213),
                        (0.587302, 0.625727, 0.625727),
                        (0.603175, 0.634126, 0.634126),
                        (0.619048, 0.642416, 0.642416),
                        (0.634921, 0.650600, 0.650600),
                        (0.650794, 0.658682, 0.658682),
                        (0.666667, 0.666667, 0.666667),
                        (0.682540, 0.674556, 0.674556),
                        (0.698413, 0.682355, 0.682355),
                        (0.714286, 0.690066, 0.690066),
                        (0.730159, 0.697691, 0.697691),
                        (0.746032, 0.705234, 0.705234),
                        (0.761905, 0.727166, 0.727166),
                        (0.777778, 0.748455, 0.748455),
                        (0.793651, 0.769156, 0.769156),
                        (0.809524, 0.789314, 0.789314),
                        (0.825397, 0.808969, 0.808969),
                        (0.841270, 0.828159, 0.828159),
                        (0.857143, 0.846913, 0.846913),
                        (0.873016, 0.865261, 0.865261),
                        (0.888889, 0.883229, 0.883229),
                        (0.904762, 0.900837, 0.900837),
                        (0.920635, 0.918109, 0.918109),
                        (0.936508, 0.935061, 0.935061),
                        (0.952381, 0.951711, 0.951711),
                        (0.968254, 0.968075, 0.968075),
                        (0.984127, 0.984167, 0.984167), (1.0, 1.0, 1.0))}

_spring_data = {'red':   ((0., 1., 1.), (1.0, 1.0, 1.0)),
                'green': ((0., 0., 0.), (1.0, 1.0, 1.0)),
                'blue':  ((0., 1., 1.), (1.0, 0.0, 0.0))}


_summer_data = {'red':   ((0., 0., 0.), (1.0, 1.0, 1.0)),
                'green': ((0., 0.5, 0.5), (1.0, 1.0, 1.0)),
                'blue':  ((0., 0.4, 0.4), (1.0, 0.4, 0.4))}


_winter_data = {'red':   ((0., 0., 0.), (1.0, 0.0, 0.0)),
                'green': ((0., 0., 0.), (1.0, 1.0, 1.0)),
                'blue':  ((0., 1., 1.), (1.0, 0.5, 0.5))}

_nipy_spectral_data = {
    'red': [
        (0.0, 0.0, 0.0), (0.05, 0.4667, 0.4667),
        (0.10, 0.5333, 0.5333), (0.15, 0.0, 0.0),
        (0.20, 0.0, 0.0), (0.25, 0.0, 0.0),
        (0.30, 0.0, 0.0), (0.35, 0.0, 0.0),
        (0.40, 0.0, 0.0), (0.45, 0.0, 0.0),
        (0.50, 0.0, 0.0), (0.55, 0.0, 0.0),
        (0.60, 0.0, 0.0), (0.65, 0.7333, 0.7333),
        (0.70, 0.9333, 0.9333), (0.75, 1.0, 1.0),
        (0.80, 1.0, 1.0), (0.85, 1.0, 1.0),
        (0.90, 0.8667, 0.8667), (0.95, 0.80, 0.80),
        (1.0, 0.80, 0.80),
    ],
    'green': [
        (0.0, 0.0, 0.0), (0.05, 0.0, 0.0),
        (0.10, 0.0, 0.0), (0.15, 0.0, 0.0),
        (0.20, 0.0, 0.0), (0.25, 0.4667, 0.4667),
        (0.30, 0.6000, 0.6000), (0.35, 0.6667, 0.6667),
        (0.40, 0.6667, 0.6667), (0.45, 0.6000, 0.6000),
        (0.50, 0.7333, 0.7333), (0.55, 0.8667, 0.8667),
        (0.60, 1.0, 1.0), (0.65, 1.0, 1.0),
        (0.70, 0.9333, 0.9333), (0.75, 0.8000, 0.8000),
        (0.80, 0.6000, 0.6000), (0.85, 0.0, 0.0),
        (0.90, 0.0, 0.0), (0.95, 0.0, 0.0),
        (1.0, 0.80, 0.80),
    ],
    'blue': [
        (0.0, 0.0, 0.0), (0.05, 0.5333, 0.5333),
        (0.10, 0.6000, 0.6000), (0.15, 0.6667, 0.6667),
        (0.20, 0.8667, 0.8667), (0.25, 0.8667, 0.8667),
        (0.30, 0.8667, 0.8667), (0.35, 0.6667, 0.6667),
        (0.40, 0.5333, 0.5333), (0.45, 0.0, 0.0),
        (0.5, 0.0, 0.0), (0.55, 0.0, 0.0),
        (0.60, 0.0, 0.0), (0.65, 0.0, 0.0),
        (0.70, 0.0, 0.0), (0.75, 0.0, 0.0),
        (0.80, 0.0, 0.0), (0.85, 0.0, 0.0),
        (0.90, 0.0, 0.0), (0.95, 0.0, 0.0),
        (1.0, 0.80, 0.80),
    ],
}


# 34 colormaps based on color specifications and designs
# developed by Cynthia Brewer (https://colorbrewer2.org/).
# The ColorBrewer palettes have been included under the terms
# of an Apache-stype license (for details, see the file
# LICENSE_COLORBREWER in the license directory of the matplotlib
# source distribution).

# RGB values taken from Brewer's Excel sheet, divided by 255

_Blues_data = (
    (0.96862745098039216,  0.98431372549019602,  1.0                ),
    (0.87058823529411766,  0.92156862745098034,  0.96862745098039216),
    (0.77647058823529413,  0.85882352941176465,  0.93725490196078431),
    (0.61960784313725492,  0.792156862745098  ,  0.88235294117647056),
    (0.41960784313725491,  0.68235294117647061,  0.83921568627450982),
    (0.25882352941176473,  0.5725490196078431 ,  0.77647058823529413),
    (0.12941176470588237,  0.44313725490196076,  0.70980392156862748),
    (0.03137254901960784,  0.31764705882352939,  0.61176470588235299),
    (0.03137254901960784,  0.18823529411764706,  0.41960784313725491)
    )

_BrBG_data = (
    (0.32941176470588235,  0.18823529411764706,  0.0196078431372549 ),
    (0.5490196078431373 ,  0.31764705882352939,  0.0392156862745098 ),
    (0.74901960784313726,  0.50588235294117645,  0.17647058823529413),
    (0.87450980392156863,  0.76078431372549016,  0.49019607843137253),
    (0.96470588235294119,  0.90980392156862744,  0.76470588235294112),
    (0.96078431372549022,  0.96078431372549022,  0.96078431372549022),
    (0.7803921568627451 ,  0.91764705882352937,  0.89803921568627454),
    (0.50196078431372548,  0.80392156862745101,  0.75686274509803919),
    (0.20784313725490197,  0.59215686274509804,  0.5607843137254902 ),
    (0.00392156862745098,  0.4                ,  0.36862745098039218),
    (0.0                ,  0.23529411764705882,  0.18823529411764706)
    )

_BuGn_data = (
    (0.96862745098039216,  0.9882352941176471 ,  0.99215686274509807),
    (0.89803921568627454,  0.96078431372549022,  0.97647058823529409),
    (0.8                ,  0.92549019607843142,  0.90196078431372551),
    (0.6                ,  0.84705882352941175,  0.78823529411764703),
    (0.4                ,  0.76078431372549016,  0.64313725490196083),
    (0.25490196078431371,  0.68235294117647061,  0.46274509803921571),
    (0.13725490196078433,  0.54509803921568623,  0.27058823529411763),
    (0.0                ,  0.42745098039215684,  0.17254901960784313),
    (0.0                ,  0.26666666666666666,  0.10588235294117647)
    )

_BuPu_data = (
    (0.96862745098039216,  0.9882352941176471 ,  0.99215686274509807),
    (0.8784313725490196 ,  0.92549019607843142,  0.95686274509803926),
    (0.74901960784313726,  0.82745098039215681,  0.90196078431372551),
    (0.61960784313725492,  0.73725490196078436,  0.85490196078431369),
    (0.5490196078431373 ,  0.58823529411764708,  0.77647058823529413),
    (0.5490196078431373 ,  0.41960784313725491,  0.69411764705882351),
    (0.53333333333333333,  0.25490196078431371,  0.61568627450980395),
    (0.50588235294117645,  0.05882352941176471,  0.48627450980392156),
    (0.30196078431372547,  0.0                ,  0.29411764705882354)
    )

_GnBu_data = (
    (0.96862745098039216,  0.9882352941176471 ,  0.94117647058823528),
    (0.8784313725490196 ,  0.95294117647058818,  0.85882352941176465),
    (0.8                ,  0.92156862745098034,  0.77254901960784317),
    (0.6588235294117647 ,  0.8666666666666667 ,  0.70980392156862748),
    (0.4823529411764706 ,  0.8                ,  0.7686274509803922 ),
    (0.30588235294117649,  0.70196078431372544,  0.82745098039215681),
    (0.16862745098039217,  0.5490196078431373 ,  0.74509803921568629),
    (0.03137254901960784,  0.40784313725490196,  0.67450980392156867),
    (0.03137254901960784,  0.25098039215686274,  0.50588235294117645)
    )

_Greens_data = (
    (0.96862745098039216,  0.9882352941176471 ,  0.96078431372549022),
    (0.89803921568627454,  0.96078431372549022,  0.8784313725490196 ),
    (0.7803921568627451 ,  0.9137254901960784 ,  0.75294117647058822),
    (0.63137254901960782,  0.85098039215686272,  0.60784313725490191),
    (0.45490196078431372,  0.7686274509803922 ,  0.46274509803921571),
    (0.25490196078431371,  0.6705882352941176 ,  0.36470588235294116),
    (0.13725490196078433,  0.54509803921568623,  0.27058823529411763),
    (0.0                ,  0.42745098039215684,  0.17254901960784313),
    (0.0                ,  0.26666666666666666,  0.10588235294117647)
    )

_Greys_data = (
    (1.0                ,  1.0                ,  1.0                ),
    (0.94117647058823528,  0.94117647058823528,  0.94117647058823528),
    (0.85098039215686272,  0.85098039215686272,  0.85098039215686272),
    (0.74117647058823533,  0.74117647058823533,  0.74117647058823533),
    (0.58823529411764708,  0.58823529411764708,  0.58823529411764708),
    (0.45098039215686275,  0.45098039215686275,  0.45098039215686275),
    (0.32156862745098042,  0.32156862745098042,  0.32156862745098042),
    (0.14509803921568629,  0.14509803921568629,  0.14509803921568629),
    (0.0                ,  0.0                ,  0.0                )
    )

_Oranges_data = (
    (1.0                ,  0.96078431372549022,  0.92156862745098034),
    (0.99607843137254903,  0.90196078431372551,  0.80784313725490198),
    (0.99215686274509807,  0.81568627450980391,  0.63529411764705879),
    (0.99215686274509807,  0.68235294117647061,  0.41960784313725491),
    (0.99215686274509807,  0.55294117647058827,  0.23529411764705882),
    (0.94509803921568625,  0.41176470588235292,  0.07450980392156863),
    (0.85098039215686272,  0.28235294117647058,  0.00392156862745098),
    (0.65098039215686276,  0.21176470588235294,  0.01176470588235294),
    (0.49803921568627452,  0.15294117647058825,  0.01568627450980392)
    )

_OrRd_data = (
    (1.0                ,  0.96862745098039216,  0.92549019607843142),
    (0.99607843137254903,  0.90980392156862744,  0.78431372549019607),
    (0.99215686274509807,  0.83137254901960789,  0.61960784313725492),
    (0.99215686274509807,  0.73333333333333328,  0.51764705882352946),
    (0.9882352941176471 ,  0.55294117647058827,  0.34901960784313724),
    (0.93725490196078431,  0.396078431372549  ,  0.28235294117647058),
    (0.84313725490196079,  0.18823529411764706,  0.12156862745098039),
    (0.70196078431372544,  0.0                ,  0.0                ),
    (0.49803921568627452,  0.0                ,  0.0                )
    )

_PiYG_data = (
    (0.55686274509803924,  0.00392156862745098,  0.32156862745098042),
    (0.77254901960784317,  0.10588235294117647,  0.49019607843137253),
    (0.87058823529411766,  0.46666666666666667,  0.68235294117647061),
    (0.94509803921568625,  0.71372549019607845,  0.85490196078431369),
    (0.99215686274509807,  0.8784313725490196 ,  0.93725490196078431),
    (0.96862745098039216,  0.96862745098039216,  0.96862745098039216),
    (0.90196078431372551,  0.96078431372549022,  0.81568627450980391),
    (0.72156862745098038,  0.88235294117647056,  0.52549019607843139),
    (0.49803921568627452,  0.73725490196078436,  0.25490196078431371),
    (0.30196078431372547,  0.5725490196078431 ,  0.12941176470588237),
    (0.15294117647058825,  0.39215686274509803,  0.09803921568627451)
    )

_PRGn_data = (
    (0.25098039215686274,  0.0                ,  0.29411764705882354),
    (0.46274509803921571,  0.16470588235294117,  0.51372549019607838),
    (0.6                ,  0.4392156862745098 ,  0.6705882352941176 ),
    (0.76078431372549016,  0.6470588235294118 ,  0.81176470588235294),
    (0.90588235294117647,  0.83137254901960789,  0.90980392156862744),
    (0.96862745098039216,  0.96862745098039216,  0.96862745098039216),
    (0.85098039215686272,  0.94117647058823528,  0.82745098039215681),
    (0.65098039215686276,  0.85882352941176465,  0.62745098039215685),
    (0.35294117647058826,  0.68235294117647061,  0.38039215686274508),
    (0.10588235294117647,  0.47058823529411764,  0.21568627450980393),
    (0.0                ,  0.26666666666666666,  0.10588235294117647)
    )

_PuBu_data = (
    (1.0                ,  0.96862745098039216,  0.98431372549019602),
    (0.92549019607843142,  0.90588235294117647,  0.94901960784313721),
    (0.81568627450980391,  0.81960784313725488,  0.90196078431372551),
    (0.65098039215686276,  0.74117647058823533,  0.85882352941176465),
    (0.45490196078431372,  0.66274509803921566,  0.81176470588235294),
    (0.21176470588235294,  0.56470588235294117,  0.75294117647058822),
    (0.0196078431372549 ,  0.4392156862745098 ,  0.69019607843137254),
    (0.01568627450980392,  0.35294117647058826,  0.55294117647058827),
    (0.00784313725490196,  0.2196078431372549 ,  0.34509803921568627)
    )

_PuBuGn_data = (
    (1.0                ,  0.96862745098039216,  0.98431372549019602),
    (0.92549019607843142,  0.88627450980392153,  0.94117647058823528),
    (0.81568627450980391,  0.81960784313725488,  0.90196078431372551),
    (0.65098039215686276,  0.74117647058823533,  0.85882352941176465),
    (0.40392156862745099,  0.66274509803921566,  0.81176470588235294),
    (0.21176470588235294,  0.56470588235294117,  0.75294117647058822),
    (0.00784313725490196,  0.50588235294117645,  0.54117647058823526),
    (0.00392156862745098,  0.42352941176470588,  0.34901960784313724),
    (0.00392156862745098,  0.27450980392156865,  0.21176470588235294)
    )

_PuOr_data = (
    (0.49803921568627452,  0.23137254901960785,  0.03137254901960784),
    (0.70196078431372544,  0.34509803921568627,  0.02352941176470588),
    (0.8784313725490196 ,  0.50980392156862742,  0.07843137254901961),
    (0.99215686274509807,  0.72156862745098038,  0.38823529411764707),
    (0.99607843137254903,  0.8784313725490196 ,  0.71372549019607845),
    (0.96862745098039216,  0.96862745098039216,  0.96862745098039216),
    (0.84705882352941175,  0.85490196078431369,  0.92156862745098034),
    (0.69803921568627447,  0.6705882352941176 ,  0.82352941176470584),
    (0.50196078431372548,  0.45098039215686275,  0.67450980392156867),
    (0.32941176470588235,  0.15294117647058825,  0.53333333333333333),
    (0.17647058823529413,  0.0                ,  0.29411764705882354)
    )

_PuRd_data = (
    (0.96862745098039216,  0.95686274509803926,  0.97647058823529409),
    (0.90588235294117647,  0.88235294117647056,  0.93725490196078431),
    (0.83137254901960789,  0.72549019607843135,  0.85490196078431369),
    (0.78823529411764703,  0.58039215686274515,  0.7803921568627451 ),
    (0.87450980392156863,  0.396078431372549  ,  0.69019607843137254),
    (0.90588235294117647,  0.16078431372549021,  0.54117647058823526),
    (0.80784313725490198,  0.07058823529411765,  0.33725490196078434),
    (0.59607843137254901,  0.0                ,  0.2627450980392157 ),
    (0.40392156862745099,  0.0                ,  0.12156862745098039)
    )

_Purples_data = (
    (0.9882352941176471 ,  0.98431372549019602,  0.99215686274509807),
    (0.93725490196078431,  0.92941176470588238,  0.96078431372549022),
    (0.85490196078431369,  0.85490196078431369,  0.92156862745098034),
    (0.73725490196078436,  0.74117647058823533,  0.86274509803921573),
    (0.61960784313725492,  0.60392156862745094,  0.78431372549019607),
    (0.50196078431372548,  0.49019607843137253,  0.72941176470588232),
    (0.41568627450980394,  0.31764705882352939,  0.63921568627450975),
    (0.32941176470588235,  0.15294117647058825,  0.5607843137254902 ),
    (0.24705882352941178,  0.0                ,  0.49019607843137253)
    )

_RdBu_data = (
    (0.40392156862745099,  0.0                ,  0.12156862745098039),
    (0.69803921568627447,  0.09411764705882353,  0.16862745098039217),
    (0.83921568627450982,  0.37647058823529411,  0.30196078431372547),
    (0.95686274509803926,  0.6470588235294118 ,  0.50980392156862742),
    (0.99215686274509807,  0.85882352941176465,  0.7803921568627451 ),
    (0.96862745098039216,  0.96862745098039216,  0.96862745098039216),
    (0.81960784313725488,  0.89803921568627454,  0.94117647058823528),
    (0.5725490196078431 ,  0.77254901960784317,  0.87058823529411766),
    (0.2627450980392157 ,  0.57647058823529407,  0.76470588235294112),
    (0.12941176470588237,  0.4                ,  0.67450980392156867),
    (0.0196078431372549 ,  0.18823529411764706,  0.38039215686274508)
    )

_RdGy_data = (
    (0.40392156862745099,  0.0                ,  0.12156862745098039),
    (0.69803921568627447,  0.09411764705882353,  0.16862745098039217),
    (0.83921568627450982,  0.37647058823529411,  0.30196078431372547),
    (0.95686274509803926,  0.6470588235294118 ,  0.50980392156862742),
    (0.99215686274509807,  0.85882352941176465,  0.7803921568627451 ),
    (1.0                ,  1.0                ,  1.0                ),
    (0.8784313725490196 ,  0.8784313725490196 ,  0.8784313725490196 ),
    (0.72941176470588232,  0.72941176470588232,  0.72941176470588232),
    (0.52941176470588236,  0.52941176470588236,  0.52941176470588236),
    (0.30196078431372547,  0.30196078431372547,  0.30196078431372547),
    (0.10196078431372549,  0.10196078431372549,  0.10196078431372549)
    )

_RdPu_data = (
    (1.0                ,  0.96862745098039216,  0.95294117647058818),
    (0.99215686274509807,  0.8784313725490196 ,  0.86666666666666667),
    (0.9882352941176471 ,  0.77254901960784317,  0.75294117647058822),
    (0.98039215686274506,  0.62352941176470589,  0.70980392156862748),
    (0.96862745098039216,  0.40784313725490196,  0.63137254901960782),
    (0.86666666666666667,  0.20392156862745098,  0.59215686274509804),
    (0.68235294117647061,  0.00392156862745098,  0.49411764705882355),
    (0.47843137254901963,  0.00392156862745098,  0.46666666666666667),
    (0.28627450980392155,  0.0                ,  0.41568627450980394)
    )

_RdYlBu_data = (
    (0.6470588235294118 , 0.0                 , 0.14901960784313725),
    (0.84313725490196079, 0.18823529411764706 , 0.15294117647058825),
    (0.95686274509803926, 0.42745098039215684 , 0.2627450980392157 ),
    (0.99215686274509807, 0.68235294117647061 , 0.38039215686274508),
    (0.99607843137254903, 0.8784313725490196  , 0.56470588235294117),
    (1.0                , 1.0                 , 0.74901960784313726),
    (0.8784313725490196 , 0.95294117647058818 , 0.97254901960784312),
    (0.6705882352941176 , 0.85098039215686272 , 0.9137254901960784 ),
    (0.45490196078431372, 0.67843137254901964 , 0.81960784313725488),
    (0.27058823529411763, 0.45882352941176469 , 0.70588235294117652),
    (0.19215686274509805, 0.21176470588235294 , 0.58431372549019611)
    )

_RdYlGn_data = (
    (0.6470588235294118 , 0.0                 , 0.14901960784313725),
    (0.84313725490196079, 0.18823529411764706 , 0.15294117647058825),
    (0.95686274509803926, 0.42745098039215684 , 0.2627450980392157 ),
    (0.99215686274509807, 0.68235294117647061 , 0.38039215686274508),
    (0.99607843137254903, 0.8784313725490196  , 0.54509803921568623),
    (1.0                , 1.0                 , 0.74901960784313726),
    (0.85098039215686272, 0.93725490196078431 , 0.54509803921568623),
    (0.65098039215686276, 0.85098039215686272 , 0.41568627450980394),
    (0.4                , 0.74117647058823533 , 0.38823529411764707),
    (0.10196078431372549, 0.59607843137254901 , 0.31372549019607843),
    (0.0                , 0.40784313725490196 , 0.21568627450980393)
    )

_Reds_data = (
    (1.0                , 0.96078431372549022 , 0.94117647058823528),
    (0.99607843137254903, 0.8784313725490196  , 0.82352941176470584),
    (0.9882352941176471 , 0.73333333333333328 , 0.63137254901960782),
    (0.9882352941176471 , 0.5725490196078431  , 0.44705882352941179),
    (0.98431372549019602, 0.41568627450980394 , 0.29019607843137257),
    (0.93725490196078431, 0.23137254901960785 , 0.17254901960784313),
    (0.79607843137254897, 0.094117647058823528, 0.11372549019607843),
    (0.6470588235294118 , 0.058823529411764705, 0.08235294117647058),
    (0.40392156862745099, 0.0                 , 0.05098039215686274)
    )

_Spectral_data = (
    (0.61960784313725492, 0.003921568627450980, 0.25882352941176473),
    (0.83529411764705885, 0.24313725490196078 , 0.30980392156862746),
    (0.95686274509803926, 0.42745098039215684 , 0.2627450980392157 ),
    (0.99215686274509807, 0.68235294117647061 , 0.38039215686274508),
    (0.99607843137254903, 0.8784313725490196  , 0.54509803921568623),
    (1.0                , 1.0                 , 0.74901960784313726),
    (0.90196078431372551, 0.96078431372549022 , 0.59607843137254901),
    (0.6705882352941176 , 0.8666666666666667  , 0.64313725490196083),
    (0.4                , 0.76078431372549016 , 0.6470588235294118 ),
    (0.19607843137254902, 0.53333333333333333 , 0.74117647058823533),
    (0.36862745098039218, 0.30980392156862746 , 0.63529411764705879)
    )

_YlGn_data = (
    (1.0                , 1.0                 , 0.89803921568627454),
    (0.96862745098039216, 0.9882352941176471  , 0.72549019607843135),
    (0.85098039215686272, 0.94117647058823528 , 0.63921568627450975),
    (0.67843137254901964, 0.8666666666666667  , 0.55686274509803924),
    (0.47058823529411764, 0.77647058823529413 , 0.47450980392156861),
    (0.25490196078431371, 0.6705882352941176  , 0.36470588235294116),
    (0.13725490196078433, 0.51764705882352946 , 0.2627450980392157 ),
    (0.0                , 0.40784313725490196 , 0.21568627450980393),
    (0.0                , 0.27058823529411763 , 0.16078431372549021)
    )

_YlGnBu_data = (
    (1.0                , 1.0                 , 0.85098039215686272),
    (0.92941176470588238, 0.97254901960784312 , 0.69411764705882351),
    (0.7803921568627451 , 0.9137254901960784  , 0.70588235294117652),
    (0.49803921568627452, 0.80392156862745101 , 0.73333333333333328),
    (0.25490196078431371, 0.71372549019607845 , 0.7686274509803922 ),
    (0.11372549019607843, 0.56862745098039214 , 0.75294117647058822),
    (0.13333333333333333, 0.36862745098039218 , 0.6588235294117647 ),
    (0.14509803921568629, 0.20392156862745098 , 0.58039215686274515),
    (0.03137254901960784, 0.11372549019607843 , 0.34509803921568627)
    )

_YlOrBr_data = (
    (1.0                , 1.0                 , 0.89803921568627454),
    (1.0                , 0.96862745098039216 , 0.73725490196078436),
    (0.99607843137254903, 0.8901960784313725  , 0.56862745098039214),
    (0.99607843137254903, 0.7686274509803922  , 0.30980392156862746),
    (0.99607843137254903, 0.6                 , 0.16078431372549021),
    (0.92549019607843142, 0.4392156862745098  , 0.07843137254901961),
    (0.8                , 0.29803921568627451 , 0.00784313725490196),
    (0.6                , 0.20392156862745098 , 0.01568627450980392),
    (0.4                , 0.14509803921568629 , 0.02352941176470588)
    )

_YlOrRd_data = (
    (1.0                , 1.0                 , 0.8                ),
    (1.0                , 0.92941176470588238 , 0.62745098039215685),
    (0.99607843137254903, 0.85098039215686272 , 0.46274509803921571),
    (0.99607843137254903, 0.69803921568627447 , 0.29803921568627451),
    (0.99215686274509807, 0.55294117647058827 , 0.23529411764705882),
    (0.9882352941176471 , 0.30588235294117649 , 0.16470588235294117),
    (0.8901960784313725 , 0.10196078431372549 , 0.10980392156862745),
    (0.74117647058823533, 0.0                 , 0.14901960784313725),
    (0.50196078431372548, 0.0                 , 0.14901960784313725)
    )


# ColorBrewer's qualitative maps, implemented using ListedColormap
# for use with mpl.colors.NoNorm

_Accent_data = (
    (0.49803921568627452, 0.78823529411764703, 0.49803921568627452),
    (0.74509803921568629, 0.68235294117647061, 0.83137254901960789),
    (0.99215686274509807, 0.75294117647058822, 0.52549019607843139),
    (1.0,                 1.0,                 0.6                ),
    (0.2196078431372549,  0.42352941176470588, 0.69019607843137254),
    (0.94117647058823528, 0.00784313725490196, 0.49803921568627452),
    (0.74901960784313726, 0.35686274509803922, 0.09019607843137254),
    (0.4,                 0.4,                 0.4                ),
    )

_Dark2_data = (
    (0.10588235294117647, 0.61960784313725492, 0.46666666666666667),
    (0.85098039215686272, 0.37254901960784315, 0.00784313725490196),
    (0.45882352941176469, 0.4392156862745098,  0.70196078431372544),
    (0.90588235294117647, 0.16078431372549021, 0.54117647058823526),
    (0.4,                 0.65098039215686276, 0.11764705882352941),
    (0.90196078431372551, 0.6705882352941176,  0.00784313725490196),
    (0.65098039215686276, 0.46274509803921571, 0.11372549019607843),
    (0.4,                 0.4,                 0.4                ),
    )

_Paired_data = (
    (0.65098039215686276, 0.80784313725490198, 0.8901960784313725 ),
    (0.12156862745098039, 0.47058823529411764, 0.70588235294117652),
    (0.69803921568627447, 0.87450980392156863, 0.54117647058823526),
    (0.2,                 0.62745098039215685, 0.17254901960784313),
    (0.98431372549019602, 0.60392156862745094, 0.6                ),
    (0.8901960784313725,  0.10196078431372549, 0.10980392156862745),
    (0.99215686274509807, 0.74901960784313726, 0.43529411764705883),
    (1.0,                 0.49803921568627452, 0.0                ),
    (0.792156862745098,   0.69803921568627447, 0.83921568627450982),
    (0.41568627450980394, 0.23921568627450981, 0.60392156862745094),
    (1.0,                 1.0,                 0.6                ),
    (0.69411764705882351, 0.34901960784313724, 0.15686274509803921),
    )

_Pastel1_data = (
    (0.98431372549019602, 0.70588235294117652, 0.68235294117647061),
    (0.70196078431372544, 0.80392156862745101, 0.8901960784313725 ),
    (0.8,                 0.92156862745098034, 0.77254901960784317),
    (0.87058823529411766, 0.79607843137254897, 0.89411764705882357),
    (0.99607843137254903, 0.85098039215686272, 0.65098039215686276),
    (1.0,                 1.0,                 0.8                ),
    (0.89803921568627454, 0.84705882352941175, 0.74117647058823533),
    (0.99215686274509807, 0.85490196078431369, 0.92549019607843142),
    (0.94901960784313721, 0.94901960784313721, 0.94901960784313721),
    )

_Pastel2_data = (
    (0.70196078431372544, 0.88627450980392153, 0.80392156862745101),
    (0.99215686274509807, 0.80392156862745101, 0.67450980392156867),
    (0.79607843137254897, 0.83529411764705885, 0.90980392156862744),
    (0.95686274509803926, 0.792156862745098,   0.89411764705882357),
    (0.90196078431372551, 0.96078431372549022, 0.78823529411764703),
    (1.0,                 0.94901960784313721, 0.68235294117647061),
    (0.94509803921568625, 0.88627450980392153, 0.8                ),
    (0.8,                 0.8,                 0.8                ),
    )

_Set1_data = (
    (0.89411764705882357, 0.10196078431372549, 0.10980392156862745),
    (0.21568627450980393, 0.49411764705882355, 0.72156862745098038),
    (0.30196078431372547, 0.68627450980392157, 0.29019607843137257),
    (0.59607843137254901, 0.30588235294117649, 0.63921568627450975),
    (1.0,                 0.49803921568627452, 0.0                ),
    (1.0,                 1.0,                 0.2                ),
    (0.65098039215686276, 0.33725490196078434, 0.15686274509803921),
    (0.96862745098039216, 0.50588235294117645, 0.74901960784313726),
    (0.6,                 0.6,                 0.6),
    )

_Set2_data = (
    (0.4,                 0.76078431372549016, 0.6470588235294118 ),
    (0.9882352941176471,  0.55294117647058827, 0.3843137254901961 ),
    (0.55294117647058827, 0.62745098039215685, 0.79607843137254897),
    (0.90588235294117647, 0.54117647058823526, 0.76470588235294112),
    (0.65098039215686276, 0.84705882352941175, 0.32941176470588235),
    (1.0,                 0.85098039215686272, 0.18431372549019609),
    (0.89803921568627454, 0.7686274509803922,  0.58039215686274515),
    (0.70196078431372544, 0.70196078431372544, 0.70196078431372544),
    )

_Set3_data = (
    (0.55294117647058827, 0.82745098039215681, 0.7803921568627451 ),
    (1.0,                 1.0,                 0.70196078431372544),
    (0.74509803921568629, 0.72941176470588232, 0.85490196078431369),
    (0.98431372549019602, 0.50196078431372548, 0.44705882352941179),
    (0.50196078431372548, 0.69411764705882351, 0.82745098039215681),
    (0.99215686274509807, 0.70588235294117652, 0.3843137254901961 ),
    (0.70196078431372544, 0.87058823529411766, 0.41176470588235292),
    (0.9882352941176471,  0.80392156862745101, 0.89803921568627454),
    (0.85098039215686272, 0.85098039215686272, 0.85098039215686272),
    (0.73725490196078436, 0.50196078431372548, 0.74117647058823533),
    (0.8,                 0.92156862745098034, 0.77254901960784317),
    (1.0,                 0.92941176470588238, 0.43529411764705883),
    )


# The next 7 palettes are from the Yorick scientific visualization package,
# an evolution of the GIST package, both by David H. Munro.
# They are released under a BSD-like license (see LICENSE_YORICK in
# the license directory of the matplotlib source distribution).
#
# Most palette functions have been reduced to simple function descriptions
# by Reinier Heeres, since the rgb components were mostly straight lines.
# gist_earth_data and gist_ncar_data were simplified by a script and some
# manual effort.

_gist_earth_data = {
    'red': (
        (0.0, 0.0, 0.0000),
        (0.2824, 0.1882, 0.1882),
        (0.4588, 0.2714, 0.2714),
        (0.5490, 0.4719, 0.4719),
        (0.6980, 0.7176, 0.7176),
        (0.7882, 0.7553, 0.7553),
        (1.0000, 0.9922, 0.9922),
    ),
    'green': (
        (0.0, 0.0, 0.0000),
        (0.0275, 0.0000, 0.0000),
        (0.1098, 0.1893, 0.1893),
        (0.1647, 0.3035, 0.3035),
        (0.2078, 0.3841, 0.3841),
        (0.2824, 0.5020, 0.5020),
        (0.5216, 0.6397, 0.6397),
        (0.6980, 0.7171, 0.7171),
        (0.7882, 0.6392, 0.6392),
        (0.7922, 0.6413, 0.6413),
        (0.8000, 0.6447, 0.6447),
        (0.8078, 0.6481, 0.6481),
        (0.8157, 0.6549, 0.6549),
        (0.8667, 0.6991, 0.6991),
        (0.8745, 0.7103, 0.7103),
        (0.8824, 0.7216, 0.7216),
        (0.8902, 0.7323, 0.7323),
        (0.8980, 0.7430, 0.7430),
        (0.9412, 0.8275, 0.8275),
        (0.9569, 0.8635, 0.8635),
        (0.9647, 0.8816, 0.8816),
        (0.9961, 0.9733, 0.9733),
        (1.0000, 0.9843, 0.9843),
    ),
    'blue': (
        (0.0, 0.0, 0.0000),
        (0.0039, 0.1684, 0.1684),
        (0.0078, 0.2212, 0.2212),
        (0.0275, 0.4329, 0.4329),
        (0.0314, 0.4549, 0.4549),
        (0.2824, 0.5004, 0.5004),
        (0.4667, 0.2748, 0.2748),
        (0.5451, 0.3205, 0.3205),
        (0.7843, 0.3961, 0.3961),
        (0.8941, 0.6651, 0.6651),
        (1.0000, 0.9843, 0.9843),
    )
}

_gist_gray_data = {
        'red': gfunc[3],
        'green': gfunc[3],
        'blue': gfunc[3],
}

def _gist_heat_red(x): return 1.5 * x
def _gist_heat_green(x): return 2 * x - 1
def _gist_heat_blue(x): return 4 * x - 3
_gist_heat_data = {
    'red': _gist_heat_red, 'green': _gist_heat_green, 'blue': _gist_heat_blue}

_gist_ncar_data = {
    'red': (
        (0.0, 0.0, 0.0000),
        (0.3098, 0.0000, 0.0000),
        (0.3725, 0.3993, 0.3993),
        (0.4235, 0.5003, 0.5003),
        (0.5333, 1.0000, 1.0000),
        (0.7922, 1.0000, 1.0000),
        (0.8471, 0.6218, 0.6218),
        (0.8980, 0.9235, 0.9235),
        (1.0000, 0.9961, 0.9961),
    ),
    'green': (
        (0.0, 0.0, 0.0000),
        (0.0510, 0.3722, 0.3722),
        (0.1059, 0.0000, 0.0000),
        (0.1569, 0.7202, 0.7202),
        (0.1608, 0.7537, 0.7537),
        (0.1647, 0.7752, 0.7752),
        (0.2157, 1.0000, 1.0000),
        (0.2588, 0.9804, 0.9804),
        (0.2706, 0.9804, 0.9804),
        (0.3176, 1.0000, 1.0000),
        (0.3686, 0.8081, 0.8081),
        (0.4275, 1.0000, 1.0000),
        (0.5216, 1.0000, 1.0000),
        (0.6314, 0.7292, 0.7292),
        (0.6863, 0.2796, 0.2796),
        (0.7451, 0.0000, 0.0000),
        (0.7922, 0.0000, 0.0000),
        (0.8431, 0.1753, 0.1753),
        (0.8980, 0.5000, 0.5000),
        (1.0000, 0.9725, 0.9725),
    ),
    'blue': (
        (0.0, 0.5020, 0.5020),
        (0.0510, 0.0222, 0.0222),
        (0.1098, 1.0000, 1.0000),
        (0.2039, 1.0000, 1.0000),
        (0.2627, 0.6145, 0.6145),
        (0.3216, 0.0000, 0.0000),
        (0.4157, 0.0000, 0.0000),
        (0.4745, 0.2342, 0.2342),
        (0.5333, 0.0000, 0.0000),
        (0.5804, 0.0000, 0.0000),
        (0.6314, 0.0549, 0.0549),
        (0.6902, 0.0000, 0.0000),
        (0.7373, 0.0000, 0.0000),
        (0.7922, 0.9738, 0.9738),
        (0.8000, 1.0000, 1.0000),
        (0.8431, 1.0000, 1.0000),
        (0.8980, 0.9341, 0.9341),
        (1.0000, 0.9961, 0.9961),
    )
}

_gist_rainbow_data = (
        (0.000, (1.00, 0.00, 0.16)),
        (0.030, (1.00, 0.00, 0.00)),
        (0.215, (1.00, 1.00, 0.00)),
        (0.400, (0.00, 1.00, 0.00)),
        (0.586, (0.00, 1.00, 1.00)),
        (0.770, (0.00, 0.00, 1.00)),
        (0.954, (1.00, 0.00, 1.00)),
        (1.000, (1.00, 0.00, 0.75))
)

_gist_stern_data = {
        'red': (
            (0.000, 0.000, 0.000), (0.0547, 1.000, 1.000),
            (0.250, 0.027, 0.250),  # (0.2500, 0.250, 0.250),
            (1.000, 1.000, 1.000)),
        'green': ((0, 0, 0), (1, 1, 1)),
        'blue': (
            (0.000, 0.000, 0.000), (0.500, 1.000, 1.000),
            (0.735, 0.000, 0.000), (1.000, 1.000, 1.000))
}

def _gist_yarg(x): return 1 - x
_gist_yarg_data = {'red': _gist_yarg, 'green': _gist_yarg, 'blue': _gist_yarg}

# This bipolar colormap was generated from CoolWarmFloat33.csv of
# "Diverging Color Maps for Scientific Visualization" by Kenneth Moreland.
# <http://www.kennethmoreland.com/color-maps/>
_coolwarm_data = {
    'red': [
        (0.0, 0.2298057, 0.2298057),
        (0.03125, 0.26623388, 0.26623388),
        (0.0625, 0.30386891, 0.30386891),
        (0.09375, 0.342804478, 0.342804478),
        (0.125, 0.38301334, 0.38301334),
        (0.15625, 0.424369608, 0.424369608),
        (0.1875, 0.46666708, 0.46666708),
        (0.21875, 0.509635204, 0.509635204),
        (0.25, 0.552953156, 0.552953156),
        (0.28125, 0.596262162, 0.596262162),
        (0.3125, 0.639176211, 0.639176211),
        (0.34375, 0.681291281, 0.681291281),
        (0.375, 0.722193294, 0.722193294),
        (0.40625, 0.761464949, 0.761464949),
        (0.4375, 0.798691636, 0.798691636),
        (0.46875, 0.833466556, 0.833466556),
        (0.5, 0.865395197, 0.865395197),
        (0.53125, 0.897787179, 0.897787179),
        (0.5625, 0.924127593, 0.924127593),
        (0.59375, 0.944468518, 0.944468518),
        (0.625, 0.958852946, 0.958852946),
        (0.65625, 0.96732803, 0.96732803),
        (0.6875, 0.969954137, 0.969954137),
        (0.71875, 0.966811177, 0.966811177),
        (0.75, 0.958003065, 0.958003065),
        (0.78125, 0.943660866, 0.943660866),
        (0.8125, 0.923944917, 0.923944917),
        (0.84375, 0.89904617, 0.89904617),
        (0.875, 0.869186849, 0.869186849),
        (0.90625, 0.834620542, 0.834620542),
        (0.9375, 0.795631745, 0.795631745),
        (0.96875, 0.752534934, 0.752534934),
        (1.0, 0.705673158, 0.705673158)],
    'green': [
        (0.0, 0.298717966, 0.298717966),
        (0.03125, 0.353094838, 0.353094838),
        (0.0625, 0.406535296, 0.406535296),
        (0.09375, 0.458757618, 0.458757618),
        (0.125, 0.50941904, 0.50941904),
        (0.15625, 0.558148092, 0.558148092),
        (0.1875, 0.604562568, 0.604562568),
        (0.21875, 0.648280772, 0.648280772),
        (0.25, 0.688929332, 0.688929332),
        (0.28125, 0.726149107, 0.726149107),
        (0.3125, 0.759599947, 0.759599947),
        (0.34375, 0.788964712, 0.788964712),
        (0.375, 0.813952739, 0.813952739),
        (0.40625, 0.834302879, 0.834302879),
        (0.4375, 0.849786142, 0.849786142),
        (0.46875, 0.860207984, 0.860207984),
        (0.5, 0.86541021, 0.86541021),
        (0.53125, 0.848937047, 0.848937047),
        (0.5625, 0.827384882, 0.827384882),
        (0.59375, 0.800927443, 0.800927443),
        (0.625, 0.769767752, 0.769767752),
        (0.65625, 0.734132809, 0.734132809),
        (0.6875, 0.694266682, 0.694266682),
        (0.71875, 0.650421156, 0.650421156),
        (0.75, 0.602842431, 0.602842431),
        (0.78125, 0.551750968, 0.551750968),
        (0.8125, 0.49730856, 0.49730856),
        (0.84375, 0.439559467, 0.439559467),
        (0.875, 0.378313092, 0.378313092),
        (0.90625, 0.312874446, 0.312874446),
        (0.9375, 0.24128379, 0.24128379),
        (0.96875, 0.157246067, 0.157246067),
        (1.0, 0.01555616, 0.01555616)],
    'blue': [
        (0.0, 0.753683153, 0.753683153),
        (0.03125, 0.801466763, 0.801466763),
        (0.0625, 0.84495867, 0.84495867),
        (0.09375, 0.883725899, 0.883725899),
        (0.125, 0.917387822, 0.917387822),
        (0.15625, 0.945619588, 0.945619588),
        (0.1875, 0.968154911, 0.968154911),
        (0.21875, 0.98478814, 0.98478814),
        (0.25, 0.995375608, 0.995375608),
        (0.28125, 0.999836203, 0.999836203),
        (0.3125, 0.998151185, 0.998151185),
        (0.34375, 0.990363227, 0.990363227),
        (0.375, 0.976574709, 0.976574709),
        (0.40625, 0.956945269, 0.956945269),
        (0.4375, 0.931688648, 0.931688648),
        (0.46875, 0.901068838, 0.901068838),
        (0.5, 0.865395561, 0.865395561),
        (0.53125, 0.820880546, 0.820880546),
        (0.5625, 0.774508472, 0.774508472),
        (0.59375, 0.726736146, 0.726736146),
        (0.625, 0.678007945, 0.678007945),
        (0.65625, 0.628751763, 0.628751763),
        (0.6875, 0.579375448, 0.579375448),
        (0.71875, 0.530263762, 0.530263762),
        (0.75, 0.481775914, 0.481775914),
        (0.78125, 0.434243684, 0.434243684),
        (0.8125, 0.387970225, 0.387970225),
        (0.84375, 0.343229596, 0.343229596),
        (0.875, 0.300267182, 0.300267182),
        (0.90625, 0.259301199, 0.259301199),
        (0.9375, 0.220525627, 0.220525627),
        (0.96875, 0.184115123, 0.184115123),
        (1.0, 0.150232812, 0.150232812)]
    }

# Implementation of Carey Rappaport's CMRmap.
# See `A Color Map for Effective Black-and-White Rendering of Color-Scale
# Images' by Carey Rappaport
# https://www.mathworks.com/matlabcentral/fileexchange/2662-cmrmap-m
_CMRmap_data = {'red':    ((0.000, 0.00, 0.00),
                           (0.125, 0.15, 0.15),
                           (0.250, 0.30, 0.30),
                           (0.375, 0.60, 0.60),
                           (0.500, 1.00, 1.00),
                           (0.625, 0.90, 0.90),
                           (0.750, 0.90, 0.90),
                           (0.875, 0.90, 0.90),
                           (1.000, 1.00, 1.00)),
                'green':  ((0.000, 0.00, 0.00),
                           (0.125, 0.15, 0.15),
                           (0.250, 0.15, 0.15),
                           (0.375, 0.20, 0.20),
                           (0.500, 0.25, 0.25),
                           (0.625, 0.50, 0.50),
                           (0.750, 0.75, 0.75),
                           (0.875, 0.90, 0.90),
                           (1.000, 1.00, 1.00)),
                'blue':   ((0.000, 0.00, 0.00),
                           (0.125, 0.50, 0.50),
                           (0.250, 0.75, 0.75),
                           (0.375, 0.50, 0.50),
                           (0.500, 0.15, 0.15),
                           (0.625, 0.00, 0.00),
                           (0.750, 0.10, 0.10),
                           (0.875, 0.50, 0.50),
                           (1.000, 1.00, 1.00))}


# An MIT licensed, colorblind-friendly heatmap from Wistia:
#   https://github.com/wistia/heatmap-palette
#   https://wistia.com/learn/culture/heatmaps-for-colorblindness
#
# >>> import matplotlib.colors as c
# >>> colors = ["#e4ff7a", "#ffe81a", "#ffbd00", "#ffa000", "#fc7f00"]
# >>> cm = c.LinearSegmentedColormap.from_list('wistia', colors)
# >>> _wistia_data = cm._segmentdata
# >>> del _wistia_data['alpha']
#
_wistia_data = {
    'red': [(0.0, 0.8941176470588236, 0.8941176470588236),
            (0.25, 1.0, 1.0),
            (0.5, 1.0, 1.0),
            (0.75, 1.0, 1.0),
            (1.0, 0.9882352941176471, 0.9882352941176471)],
    'green': [(0.0, 1.0, 1.0),
              (0.25, 0.9098039215686274, 0.9098039215686274),
              (0.5, 0.7411764705882353, 0.7411764705882353),
              (0.75, 0.6274509803921569, 0.6274509803921569),
              (1.0, 0.4980392156862745, 0.4980392156862745)],
    'blue': [(0.0, 0.47843137254901963, 0.47843137254901963),
             (0.25, 0.10196078431372549, 0.10196078431372549),
             (0.5, 0.0, 0.0),
             (0.75, 0.0, 0.0),
             (1.0, 0.0, 0.0)],
}


# Categorical palettes from Vega:
# https://github.com/vega/vega/wiki/Scales
# (divided by 255)
#

_tab10_data = (
    (0.12156862745098039, 0.4666666666666667,  0.7058823529411765  ),  # 1f77b4
    (1.0,                 0.4980392156862745,  0.054901960784313725),  # ff7f0e
    (0.17254901960784313, 0.6274509803921569,  0.17254901960784313 ),  # 2ca02c
    (0.8392156862745098,  0.15294117647058825, 0.1568627450980392  ),  # d62728
    (0.5803921568627451,  0.403921568627451,   0.7411764705882353  ),  # 9467bd
    (0.5490196078431373,  0.33725490196078434, 0.29411764705882354 ),  # 8c564b
    (0.8901960784313725,  0.4666666666666667,  0.7607843137254902  ),  # e377c2
    (0.4980392156862745,  0.4980392156862745,  0.4980392156862745  ),  # 7f7f7f
    (0.7372549019607844,  0.7411764705882353,  0.13333333333333333 ),  # bcbd22
    (0.09019607843137255, 0.7450980392156863,  0.8117647058823529),    # 17becf
)

_tab20_data = (
    (0.12156862745098039, 0.4666666666666667,  0.7058823529411765  ),  # 1f77b4
    (0.6823529411764706,  0.7803921568627451,  0.9098039215686274  ),  # aec7e8
    (1.0,                 0.4980392156862745,  0.054901960784313725),  # ff7f0e
    (1.0,                 0.7333333333333333,  0.47058823529411764 ),  # ffbb78
    (0.17254901960784313, 0.6274509803921569,  0.17254901960784313 ),  # 2ca02c
    (0.596078431372549,   0.8745098039215686,  0.5411764705882353  ),  # 98df8a
    (0.8392156862745098,  0.15294117647058825, 0.1568627450980392  ),  # d62728
    (1.0,                 0.596078431372549,   0.5882352941176471  ),  # ff9896
    (0.5803921568627451,  0.403921568627451,   0.7411764705882353  ),  # 9467bd
    (0.7725490196078432,  0.6901960784313725,  0.8352941176470589  ),  # c5b0d5
    (0.5490196078431373,  0.33725490196078434, 0.29411764705882354 ),  # 8c564b
    (0.7686274509803922,  0.611764705882353,   0.5803921568627451  ),  # c49c94
    (0.8901960784313725,  0.4666666666666667,  0.7607843137254902  ),  # e377c2
    (0.9686274509803922,  0.7137254901960784,  0.8235294117647058  ),  # f7b6d2
    (0.4980392156862745,  0.4980392156862745,  0.4980392156862745  ),  # 7f7f7f
    (0.7803921568627451,  0.7803921568627451,  0.7803921568627451  ),  # c7c7c7
    (0.7372549019607844,  0.7411764705882353,  0.13333333333333333 ),  # bcbd22
    (0.8588235294117647,  0.8588235294117647,  0.5529411764705883  ),  # dbdb8d
    (0.09019607843137255, 0.7450980392156863,  0.8117647058823529  ),  # 17becf
    (0.6196078431372549,  0.8549019607843137,  0.8980392156862745),    # 9edae5
)

_tab20b_data = (
    (0.2235294117647059,  0.23137254901960785, 0.4745098039215686 ),  # 393b79
    (0.3215686274509804,  0.32941176470588235, 0.6392156862745098 ),  # 5254a3
    (0.4196078431372549,  0.43137254901960786, 0.8117647058823529 ),  # 6b6ecf
    (0.611764705882353,   0.6196078431372549,  0.8705882352941177 ),  # 9c9ede
    (0.38823529411764707, 0.4745098039215686,  0.2235294117647059 ),  # 637939
    (0.5490196078431373,  0.6352941176470588,  0.3215686274509804 ),  # 8ca252
    (0.7098039215686275,  0.8117647058823529,  0.4196078431372549 ),  # b5cf6b
    (0.807843137254902,   0.8588235294117647,  0.611764705882353  ),  # cedb9c
    (0.5490196078431373,  0.42745098039215684, 0.19215686274509805),  # 8c6d31
    (0.7411764705882353,  0.6196078431372549,  0.2235294117647059 ),  # bd9e39
    (0.9058823529411765,  0.7294117647058823,  0.3215686274509804 ),  # e7ba52
    (0.9058823529411765,  0.796078431372549,   0.5803921568627451 ),  # e7cb94
    (0.5176470588235295,  0.23529411764705882, 0.2235294117647059 ),  # 843c39
    (0.6784313725490196,  0.28627450980392155, 0.2901960784313726 ),  # ad494a
    (0.8392156862745098,  0.3803921568627451,  0.4196078431372549 ),  # d6616b
    (0.9058823529411765,  0.5882352941176471,  0.611764705882353  ),  # e7969c
    (0.4823529411764706,  0.2549019607843137,  0.45098039215686275),  # 7b4173
    (0.6470588235294118,  0.3176470588235294,  0.5803921568627451 ),  # a55194
    (0.807843137254902,   0.42745098039215684, 0.7411764705882353 ),  # ce6dbd
    (0.8705882352941177,  0.6196078431372549,  0.8392156862745098 ),  # de9ed6
)

_tab20c_data = (
    (0.19215686274509805, 0.5098039215686274,  0.7411764705882353  ),  # 3182bd
    (0.4196078431372549,  0.6823529411764706,  0.8392156862745098  ),  # 6baed6
    (0.6196078431372549,  0.792156862745098,   0.8823529411764706  ),  # 9ecae1
    (0.7764705882352941,  0.8588235294117647,  0.9372549019607843  ),  # c6dbef
    (0.9019607843137255,  0.3333333333333333,  0.050980392156862744),  # e6550d
    (0.9921568627450981,  0.5529411764705883,  0.23529411764705882 ),  # fd8d3c
    (0.9921568627450981,  0.6823529411764706,  0.4196078431372549  ),  # fdae6b
    (0.9921568627450981,  0.8156862745098039,  0.6352941176470588  ),  # fdd0a2
    (0.19215686274509805, 0.6392156862745098,  0.32941176470588235 ),  # 31a354
    (0.4549019607843137,  0.7686274509803922,  0.4627450980392157  ),  # 74c476
    (0.6313725490196078,  0.8509803921568627,  0.6078431372549019  ),  # a1d99b
    (0.7803921568627451,  0.9137254901960784,  0.7529411764705882  ),  # c7e9c0
    (0.4588235294117647,  0.4196078431372549,  0.6941176470588235  ),  # 756bb1
    (0.6196078431372549,  0.6039215686274509,  0.7843137254901961  ),  # 9e9ac8
    (0.7372549019607844,  0.7411764705882353,  0.8627450980392157  ),  # bcbddc
    (0.8549019607843137,  0.8549019607843137,  0.9215686274509803  ),  # dadaeb
    (0.38823529411764707, 0.38823529411764707, 0.38823529411764707 ),  # 636363
    (0.5882352941176471,  0.5882352941176471,  0.5882352941176471  ),  # 969696
    (0.7411764705882353,  0.7411764705882353,  0.7411764705882353  ),  # bdbdbd
    (0.8509803921568627,  0.8509803921568627,  0.8509803921568627  ),  # d9d9d9
)


_petroff10_data = (
    (0.24705882352941178, 0.5647058823529412,  0.8549019607843137),    # 3f90da
    (1.0,                 0.6627450980392157,  0.054901960784313725),  # ffa90e
    (0.7411764705882353,  0.12156862745098039, 0.00392156862745098),   # bd1f01
    (0.5803921568627451,  0.6431372549019608,  0.6352941176470588),    # 94a4a2
    (0.5137254901960784,  0.17647058823529413, 0.7137254901960784),    # 832db6
    (0.6627450980392157,  0.4196078431372549,  0.34901960784313724),   # a96b59
    (0.9058823529411765,  0.38823529411764707, 0.0),                   # e76300
    (0.7254901960784313,  0.6745098039215687,  0.4392156862745098),    # b9ac70
    (0.44313725490196076, 0.4588235294117647,  0.5058823529411764),    # 717581
    (0.5725490196078431,  0.8549019607843137,  0.8666666666666667),    # 92dadd
)


datad = {
    'Blues': _Blues_data,
    'BrBG': _BrBG_data,
    'BuGn': _BuGn_data,
    'BuPu': _BuPu_data,
    'CMRmap': _CMRmap_data,
    'GnBu': _GnBu_data,
    'Greens': _Greens_data,
    'Greys': _Greys_data,
    'OrRd': _OrRd_data,
    'Oranges': _Oranges_data,
    'PRGn': _PRGn_data,
    'PiYG': _PiYG_data,
    'PuBu': _PuBu_data,
    'PuBuGn': _PuBuGn_data,
    'PuOr': _PuOr_data,
    'PuRd': _PuRd_data,
    'Purples': _Purples_data,
    'RdBu': _RdBu_data,
    'RdGy': _RdGy_data,
    'RdPu': _RdPu_data,
    'RdYlBu': _RdYlBu_data,
    'RdYlGn': _RdYlGn_data,
    'Reds': _Reds_data,
    'Spectral': _Spectral_data,
    'Wistia': _wistia_data,
    'YlGn': _YlGn_data,
    'YlGnBu': _YlGnBu_data,
    'YlOrBr': _YlOrBr_data,
    'YlOrRd': _YlOrRd_data,
    'afmhot': _afmhot_data,
    'autumn': _autumn_data,
    'binary': _binary_data,
    'bone': _bone_data,
    'brg': _brg_data,
    'bwr': _bwr_data,
    'cool': _cool_data,
    'coolwarm': _coolwarm_data,
    'copper': _copper_data,
    'cubehelix': _cubehelix_data,
    'flag': _flag_data,
    'gist_earth': _gist_earth_data,
    'gist_gray': _gist_gray_data,
    'gist_heat': _gist_heat_data,
    'gist_ncar': _gist_ncar_data,
    'gist_rainbow': _gist_rainbow_data,
    'gist_stern': _gist_stern_data,
    'gist_yarg': _gist_yarg_data,
    'gnuplot': _gnuplot_data,
    'gnuplot2': _gnuplot2_data,
    'gray': _gray_data,
    'hot': _hot_data,
    'hsv': _hsv_data,
    'jet': _jet_data,
    'nipy_spectral': _nipy_spectral_data,
    'ocean': _ocean_data,
    'pink': _pink_data,
    'prism': _prism_data,
    'rainbow': _rainbow_data,
    'seismic': _seismic_data,
    'spring': _spring_data,
    'summer': _summer_data,
    'terrain': _terrain_data,
    'winter': _winter_data,
    # Qualitative
    'Accent': {'listed': _Accent_data},
    'Dark2': {'listed': _Dark2_data},
    'Paired': {'listed': _Paired_data},
    'Pastel1': {'listed': _Pastel1_data},
    'Pastel2': {'listed': _Pastel2_data},
    'Set1': {'listed': _Set1_data},
    'Set2': {'listed': _Set2_data},
    'Set3': {'listed': _Set3_data},
    'tab10': {'listed': _tab10_data},
    'tab20': {'listed': _tab20_data},
    'tab20b': {'listed': _tab20b_data},
    'tab20c': {'listed': _tab20c_data},
}
