import asyncio
import json
from bs4 import BeautifulSoup
from crawl4ai import Async<PERSON>ebCrawler, CrawlerRunConfig, BrowserConfig
async def main():
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://www.jsxdyh.com/"
        )
        print(result.markdown)

async def main1():
    crawler_config = CrawlerRunConfig(
        wait_for = 'section.note-item',
        
    )
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="https://www.xiaohongshu.com/explore?channel_id=homefeed.travel_v3",
            config=crawler_config
        )
        posts =[]
        soup = BeautifulSoup(result.cleaned_html, 'html.parser')
        for article in soup.select('section'):
            try:
                #提取标题
                title_elem = section.select_one("div > div > a > span")
                title = title_elem.text.strip() if like_elem else "无标题"
                #提取点赞数
                like_elem = section.select_one("div > div > div > span > span")
                likes = like_elem.text.strip() if like_elem else 0
                #提取评论数                comment_elem = section.select_one("div > div > div > span > span")comments = comment_elem.text.strip() if comment_elem else 0
                #提取发布时间 time_elem = section.select_one("div > div > div > span > span")time = time_elem.text.strip() if time_elem else "未知时间"
                #提取文章链接
                link_elem = section.select_one("div > a:nth-child(2)")
                link = link_elem.get("href", "") if link_elem else ""
                if link and not link.startswith("http"):
                    link = f"https://www.xiaohongshu.com{link}"
                posts.append({
                    "title": title,
                    "likes": likes,
                                        "link": link
                })
            except Exception as e:
                print(f"Error processing article: {e}")
                continue

                
        print(json.dumps(posts, ensure_ascii=False, indent=2))


        

import os
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'
async def main2():...

if __name__ == "__main__":
    asyncio.run(main1())
   