"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[154],{31283:function(e,t,o){o.d(t,{o:function(){return a.Z}});var a=o(49566)},63610:function(e,t,o){o.d(t,{d:function(){return p}});var a=o(57437),r=o(2265),n=o(64482),l=o(52787),c=o(20577),i=o(13634),s=o(31283),d=o(15424),u=o(89970),h=o(19250);let p=["metadata","config","enforced_params","aliases"],g=(e,t)=>p.includes(e)||"json"===t.format,m=e=>{if(!e)return!0;try{return JSON.parse(e),!0}catch(e){return!1}},f=(e,t,o)=>{let a={max_budget:"Enter maximum budget in USD (e.g., 100.50)",budget_duration:"Select a time period for budget reset",tpm_limit:"Enter maximum tokens per minute (whole number)",rpm_limit:"Enter maximum requests per minute (whole number)",duration:"Enter duration (e.g., 30s, 24h, 7d)",metadata:'Enter JSON object with key-value pairs\nExample: {"team": "research", "project": "nlp"}',config:'Enter configuration as JSON object\nExample: {"setting": "value"}',permissions:"Enter comma-separated permission strings",enforced_params:'Enter parameters as JSON object\nExample: {"param": "value"}',blocked:"Enter true/false or specific block conditions",aliases:'Enter aliases as JSON object\nExample: {"alias1": "value1", "alias2": "value2"}',models:"Select one or more model names",key_alias:"Enter a unique identifier for this key",tags:"Enter comma-separated tag strings"}[e]||({string:"Text input",number:"Numeric input",integer:"Whole number input",boolean:"True/False value"})[o]||"Text input";return g(e,t)?"".concat(a,"\nMust be valid JSON format"):t.enum?"Select from available options\nAllowed values: ".concat(t.enum.join(", ")):a};t.Z=e=>{let{schemaComponent:t,excludedFields:o=[],form:p,overrideLabels:w={},overrideTooltips:y={},customValidation:j={},defaultValues:_={}}=e,[C,k]=(0,r.useState)(null),[v,T]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{try{let e=(await (0,h.getOpenAPISchema)()).components.schemas[t];if(!e)throw Error('Schema component "'.concat(t,'" not found'));k(e);let a={};Object.keys(e.properties).filter(e=>!o.includes(e)&&void 0!==_[e]).forEach(e=>{a[e]=_[e]}),p.setFieldsValue(a)}catch(e){console.error("Schema fetch error:",e),T(e instanceof Error?e.message:"Failed to fetch schema")}})()},[t,p,o]);let E=e=>{if(e.type)return e.type;if(e.anyOf){let t=e.anyOf.map(e=>e.type);if(t.includes("number")||t.includes("integer"))return"number";t.includes("string")}return"string"},S=(e,t)=>{var o;let r;let h=E(t),p=null==C?void 0:null===(o=C.required)||void 0===o?void 0:o.includes(e),k=w[e]||t.title||e,v=y[e]||t.description,T=[];p&&T.push({required:!0,message:"".concat(k," is required")}),j[e]&&T.push({validator:j[e]}),g(e,t)&&T.push({validator:async(e,t)=>{if(t&&!m(t))throw Error("Please enter valid JSON")}});let S=v?(0,a.jsxs)("span",{children:[k," ",(0,a.jsx)(u.Z,{title:v,children:(0,a.jsx)(d.Z,{style:{marginLeft:"4px"}})})]}):k;return r=g(e,t)?(0,a.jsx)(n.default.TextArea,{rows:4,placeholder:"Enter as JSON",className:"font-mono"}):t.enum?(0,a.jsx)(l.default,{children:t.enum.map(e=>(0,a.jsx)(l.default.Option,{value:e,children:e},e))}):"number"===h||"integer"===h?(0,a.jsx)(c.Z,{style:{width:"100%"},precision:"integer"===h?0:void 0}):"duration"===e?(0,a.jsx)(s.o,{placeholder:"eg: 30s, 30h, 30d"}):(0,a.jsx)(s.o,{placeholder:v||""}),(0,a.jsx)(i.Z.Item,{label:S,name:e,className:"mt-8",rules:T,initialValue:_[e],help:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:f(e,t,h)}),children:r},e)};return v?(0,a.jsxs)("div",{className:"text-red-500",children:["Error: ",v]}):(null==C?void 0:C.properties)?(0,a.jsx)("div",{children:Object.entries(C.properties).filter(e=>{let[t]=e;return!o.includes(t)}).map(e=>{let[t,o]=e;return S(t,o)})}):null}},9114:function(e,t,o){var a=o(57271),r=o(85968);function n(){return"topRight"}function l(e,t){var o;return"string"==typeof e?{message:t,description:e}:{message:null!==(o=e.message)&&void 0!==o?o:t,...e}}function c(e){return"number"==typeof e?e:"string"==typeof e&&/^\d+$/.test(e)?parseInt(e,10):void 0}let i=["invalid api key","invalid authorization header format","authentication error","invalid proxy server token","invalid jwt token","invalid jwt submitted","unauthorized access to metrics endpoint"],s=["admin-only endpoint","not allowed to access model","user does not have permission","access forbidden","invalid credentials used to access ui","user not allowed to access proxy"],d=["db not connected","database not initialized","no db connected","prisma client not initialized","service unhealthy"],u=["no models configured on proxy","llm router not initialized","no deployments available","no healthy deployment available","not allowed to access model due to tags configuration","invalid model name passed in"],h=["deployment over user-defined ratelimit","crossed tpm / rpm / max parallel request limit","max parallel request limit"],p=["budget exceeded","crossed budget","provider budget"],g=["must be a litellm enterprise user","only be available for liteLLM enterprise users","missing litellm-enterprise package","only available on the docker image","enterprise feature","premium user"],m=["invalid json payload","invalid request type","invalid key format","invalid hash key","invalid sort column","invalid sort order","invalid limit","invalid file type","invalid field","invalid date format"],f=["model not found","model with id","credential not found","user not found","team not found","organization not found","mcp server with id","tool '"],w=["already exists","team member is already in team","user already exists"],y=["violated openai moderation policy","violated jailbreak threshold","violated prompt_injection threshold","violated content safety policy","violated lasso guardrail policy","blocked by pillar security guardrail","violated azure prompt shield guardrail policy","content blocked by model armor","response blocked by model armor","streaming response blocked by model armor","guardrail","moderation"],j=["invalid purpose","service must be specified","invalid response - response.response is none"],_=["cloudzero settings not configured","failed to decrypt cloudzero api key","cloudzero settings not found"],C=["created successfully","updated successfully","deleted successfully","credential created successfully","model added successfully","team created successfully","user created successfully","organization created successfully","cloudzero settings initialized successfully","cloudzero settings updated successfully","cloudzero export completed successfully","mock llm request made","mock slack alert sent","mock email alert sent","spend for all api keys and teams reset successfully","monthlyglobalspend view refreshed","cache cleared successfully","cache set successfully","ip ","deleted successfully"],k=["rate limit reached for deployment","deployment cooldown period active"],v=["this feature is only available for litellm enterprise users","enterprise features are not available","regenerating virtual keys is an enterprise feature","trying to set allowed_routes. this is an enterprise feature"],T=["invalid maximum_spend_logs_retention_interval value","error has invalid or non-convertible code","failed to save health check to database"];t.Z={error(e){var t,o;let r=l(e,"Error");a.ZP.error({...r,placement:null!==(t=r.placement)&&void 0!==t?t:n(),duration:null!==(o=r.duration)&&void 0!==o?o:6})},warning(e){var t,o;let r=l(e,"Warning");a.ZP.warning({...r,placement:null!==(t=r.placement)&&void 0!==t?t:n(),duration:null!==(o=r.duration)&&void 0!==o?o:5})},info(e){var t,o;let r=l(e,"Info");a.ZP.info({...r,placement:null!==(t=r.placement)&&void 0!==t?t:n(),duration:null!==(o=r.duration)&&void 0!==o?o:4})},success(e){var t,o;let r=l(e,"Success");a.ZP.success({...r,placement:null!==(t=r.placement)&&void 0!==t?t:n(),duration:null!==(o=r.duration)&&void 0!==o?o:3.5})},fromBackend(e,t){var o,l,E,S,b,F,P,O,B,N,x,G;let J=null!==(G=null!==(x=c(null==e?void 0:null===(N=e.response)||void 0===N?void 0:N.status))&&void 0!==x?x:c(null==e?void 0:e.status_code))&&void 0!==G?G:c(null==e?void 0:e.code),A=function(e){var t,o,a,n,l,c,i,s,d,u,h,p;if("string"==typeof e)return e;let g=null!==(p=null!==(h=null!==(u=null!==(d=null!==(s=null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(o=a.data)||void 0===o?void 0:null===(t=o.error)||void 0===t?void 0:t.message)&&void 0!==s?s:null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message)&&void 0!==d?d:null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(c=i.data)||void 0===c?void 0:c.error)&&void 0!==u?u:null==e?void 0:e.detail)&&void 0!==h?h:null==e?void 0:e.message)&&void 0!==p?p:e;return(0,r.O)(g)}(e),U={...null!=t?t:{},description:A,placement:null!==(o=null==t?void 0:t.placement)&&void 0!==o?o:n()};if(void 0!==J||e instanceof Error||"string"==typeof e||e&&"object"==typeof e&&("error"in e||"detail"in e)){let e=function(e,t){var o,a,r,n,l;let c=(t||"").toLowerCase();return i.some(e=>c.includes(e))?"Authentication Error":s.some(e=>c.includes(e))?"Access Denied":(null==d?void 0:null===(o=d.some)||void 0===o?void 0:o.call(d,e=>c.includes(e)))||503===e?"Service Unavailable":(null==p?void 0:null===(a=p.some)||void 0===a?void 0:a.call(p,e=>c.includes(e)))?"Budget Exceeded":(null==g?void 0:null===(r=g.some)||void 0===r?void 0:r.call(g,e=>c.includes(e)))?"Feature Unavailable":(null==u?void 0:null===(n=u.some)||void 0===n?void 0:n.call(u,e=>c.includes(e)))?"Routing Error":w.some(e=>c.includes(e))?"Already Exists":y.some(e=>c.includes(e))?"Content Blocked":j.some(e=>c.includes(e))?"Validation Error":_.some(e=>c.includes(e))?"Integration Error":m.some(e=>c.includes(e))?"Validation Error":404===e||c.includes("not found")||f.some(e=>c.includes(e))?"Not Found":429===e||c.includes("rate limit")||c.includes("tpm")||c.includes("rpm")||(null==h?void 0:null===(l=h.some)||void 0===l?void 0:l.call(h,e=>c.includes(e)))?"Rate Limit Exceeded":e&&e>=500?"Server Error":401===e?"Authentication Error":403===e?"Access Denied":c.includes("enterprise")||c.includes("premium")?"Info":e&&e>=400?"Request Error":"Error"}(J,A),o={...U,message:e};if("Rate Limit Exceeded"===e||"Info"===e||"Budget Exceeded"===e||"Feature Unavailable"===e||"Content Blocked"===e||"Integration Error"===e){a.ZP.warning({...o,duration:null!==(l=null==t?void 0:t.duration)&&void 0!==l?l:7});return}if("Server Error"===e){a.ZP.error({...o,duration:null!==(E=null==t?void 0:t.duration)&&void 0!==E?E:8});return}if("Request Error"===e||"Authentication Error"===e||"Access Denied"===e||"Not Found"===e||"Error"===e){a.ZP.error({...o,duration:null!==(S=null==t?void 0:t.duration)&&void 0!==S?S:6});return}a.ZP.info({...o,duration:null!==(b=null==t?void 0:t.duration)&&void 0!==b?b:4});return}let R=function(e){let t=(e||"").toLowerCase();return C.some(e=>t.includes(e))?{kind:"success",title:"Success"}:v.some(e=>t.includes(e))?{kind:"warning",title:"Feature Notice"}:T.some(e=>t.includes(e))?{kind:"warning",title:"Configuration Warning"}:k.some(e=>t.includes(e))?{kind:"warning",title:"Rate Limit"}:null}(A),I={...U,message:null!==(F=null==R?void 0:R.title)&&void 0!==F?F:"Info"};if((null==R?void 0:R.kind)==="success"){a.ZP.success({...I,duration:null!==(P=null==t?void 0:t.duration)&&void 0!==P?P:3.5});return}if((null==R?void 0:R.kind)==="warning"){a.ZP.warning({...I,duration:null!==(O=null==t?void 0:t.duration)&&void 0!==O?O:6});return}a.ZP.info({...I,duration:null!==(B=null==t?void 0:t.duration)&&void 0!==B?B:4})},clear(){a.ZP.destroy()}}},19250:function(e,t,o){o.r(t),o.d(t,{DEFAULT_ORGANIZATION:function(){return p},PredictedSpendLogsCall:function(){return tl},addAllowedIP:function(){return em},adminGlobalActivity:function(){return eJ},adminGlobalActivityExceptions:function(){return eR},adminGlobalActivityExceptionsPerDeployment:function(){return eI},adminGlobalActivityPerModel:function(){return eU},adminGlobalCacheActivity:function(){return eA},adminSpendLogsCall:function(){return eB},adminTopEndUsersCall:function(){return ex},adminTopKeysCall:function(){return eN},adminTopModelsCall:function(){return eM},adminspendByProvider:function(){return eG},alertingSettingsCall:function(){return A},allEndUsersCall:function(){return eb},allTagNamesCall:function(){return eS},availableTeamListCall:function(){return W},budgetCreateCall:function(){return N},budgetDeleteCall:function(){return B},budgetUpdateCall:function(){return x},cachingHealthCheckCall:function(){return tv},callMCPTool:function(){return tq},cancelModelCostMapReload:function(){return S},claimOnboardingToken:function(){return el},convertPromptFileToJson:function(){return tx},createGuardrailCall:function(){return tJ},createMCPServer:function(){return tz},createPassThroughEndpoint:function(){return tf},createPromptCall:function(){return tO},credentialCreateCall:function(){return eQ},credentialDeleteCall:function(){return e1},credentialGetCall:function(){return e0},credentialListCall:function(){return eX},credentialUpdateCall:function(){return e2},defaultProxyBaseUrl:function(){return c},deleteAllowedIP:function(){return ef},deleteCallback:function(){return ou},deleteConfigFieldSetting:function(){return ty},deleteGuardrailCall:function(){return oe},deleteMCPServer:function(){return tD},deletePassThroughEndpointsCall:function(){return tj},deletePromptCall:function(){return tN},fetchMCPAccessGroups:function(){return tM},fetchMCPServers:function(){return tI},formatDate:function(){return l},getAllowedIPs:function(){return eg},getBudgetList:function(){return ts},getBudgetSettings:function(){return td},getCallbacksCall:function(){return tu},getConfigFieldSetting:function(){return tg},getDefaultTeamSettings:function(){return t$},getEmailEventSettings:function(){return t7},getGeneralSettingsCall:function(){return th},getGuardrailInfo:function(){return oa},getGuardrailProviderSpecificParams:function(){return oo},getGuardrailUISettings:function(){return ot},getGuardrailsList:function(){return tb},getInternalUserSettings:function(){return tU},getModelCostMapReloadStatus:function(){return b},getOnboardingCredentials:function(){return en},getOpenAPISchema:function(){return k},getPassThroughEndpointInfo:function(){return od},getPassThroughEndpointsCall:function(){return tp},getPossibleUserRoles:function(){return eK},getPromptInfo:function(){return tP},getPromptsList:function(){return tF},getProxyBaseUrl:function(){return u},getProxyUISettings:function(){return tS},getPublicModelHubInfo:function(){return C},getRemainingUsers:function(){return oi},getSSOSettings:function(){return on},getTeamPermissionsCall:function(){return tX},getTotalSpendCall:function(){return er},getUiConfig:function(){return _},healthCheckCall:function(){return tC},healthCheckHistoryCall:function(){return tT},individualModelHealthCheckCall:function(){return tk},invitationClaimCall:function(){return J},invitationCreateCall:function(){return G},keyCreateCall:function(){return R},keyCreateServiceAccountCall:function(){return U},keyDeleteCall:function(){return M},keyInfoCall:function(){return ez},keyInfoV1Call:function(){return eD},keyListCall:function(){return eV},keySpendLogsCall:function(){return ev},keyUpdateCall:function(){return e4},latestHealthChecksCall:function(){return tE},listMCPTools:function(){return tV},makeModelGroupPublic:function(){return j},mcpToolsCall:function(){return oh},modelAvailableCall:function(){return ek},modelCostMap:function(){return v},modelCreateCall:function(){return F},modelDeleteCall:function(){return O},modelExceptionsCall:function(){return e_},modelHubCall:function(){return ep},modelHubPublicModelsCall:function(){return eh},modelInfoCall:function(){return ed},modelInfoV1Call:function(){return eu},modelMetricsCall:function(){return ew},modelMetricsSlowResponsesCall:function(){return ej},modelPatchUpdateCall:function(){return e5},modelSettingsCall:function(){return P},modelUpdateCall:function(){return e6},organizationCreateCall:function(){return $},organizationDeleteCall:function(){return X},organizationInfoCall:function(){return K},organizationListCall:function(){return Y},organizationMemberAddCall:function(){return tt},organizationMemberDeleteCall:function(){return to},organizationMemberUpdateCall:function(){return ta},organizationUpdateCall:function(){return Q},patchPromptCall:function(){return tG},perUserAnalyticsCall:function(){return ok},proxyBaseUrl:function(){return s},regenerateKeyCall:function(){return ec},reloadModelCostMap:function(){return T},resetEmailEventSettings:function(){return t8},scheduleModelCostMapReload:function(){return E},serverRootPath:function(){return i},serviceHealthCheck:function(){return ti},sessionSpendLogsCall:function(){return t1},setCallbacksCall:function(){return t_},setGlobalLitellmHeaderName:function(){return y},slackBudgetAlertsHealthCheck:function(){return tc},spendUsersCall:function(){return eq},streamingModelMetricsCall:function(){return ey},tagCreateCall:function(){return tZ},tagDailyActivityCall:function(){return eo},tagDauCall:function(){return ow},tagDeleteCall:function(){return tK},tagDistinctCall:function(){return o_},tagInfoCall:function(){return tW},tagListCall:function(){return tY},tagMauCall:function(){return oj},tagUpdateCall:function(){return tH},tagWauCall:function(){return oy},tagsSpendLogsCall:function(){return eE},teamBulkMemberAddCall:function(){return e9},teamCreateCall:function(){return e$},teamDailyActivityCall:function(){return ea},teamDeleteCall:function(){return L},teamInfoCall:function(){return q},teamListCall:function(){return H},teamMemberAddCall:function(){return e7},teamMemberDeleteCall:function(){return te},teamMemberUpdateCall:function(){return e8},teamPermissionsUpdateCall:function(){return t0},teamSpendLogsCall:function(){return eT},teamUpdateCall:function(){return e3},testConnectionRequest:function(){return eL},testMCPConnectionRequest:function(){return op},testMCPToolsListRequest:function(){return og},transformRequestCall:function(){return ee},uiAuditLogsCall:function(){return oc},uiSpendLogDetailsCall:function(){return tA},uiSpendLogsCall:function(){return eO},updateConfigFieldSetting:function(){return tw},updateDefaultTeamSettings:function(){return tQ},updateEmailEventSettings:function(){return t9},updateGuardrailCall:function(){return or},updateInternalUserSettings:function(){return tR},updateMCPServer:function(){return tL},updatePassThroughEndpoint:function(){return os},updatePassThroughFieldSetting:function(){return tm},updatePromptCall:function(){return tB},updateSSOSettings:function(){return ol},updateUsefulLinksCall:function(){return eC},userAgentAnalyticsCall:function(){return of},userAgentSummaryCall:function(){return oC},userBulkUpdateUserCall:function(){return tn},userCreateCall:function(){return I},userDailyActivityAggregatedCall:function(){return eW},userDailyActivityCall:function(){return et},userDeleteCall:function(){return z},userFilterUICall:function(){return eF},userGetAllUsersCall:function(){return eY},userGetRequesedtModelsCall:function(){return eH},userInfoCall:function(){return V},userListCall:function(){return D},userRequestModelCall:function(){return eZ},userSpendLogsCall:function(){return eP},userUpdateUserCall:function(){return tr},v2TeamListCall:function(){return Z},vectorStoreCreateCall:function(){return t2},vectorStoreDeleteCall:function(){return t3},vectorStoreInfoCall:function(){return t5},vectorStoreListCall:function(){return t4},vectorStoreSearchCall:function(){return om},vectorStoreUpdateCall:function(){return t6}});var a=o(42264),r=o(63610),n=o(9114);let l=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)},c=null,i="/",s=null;console.log=function(){};let d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=window.location.origin,a=t||o;console.log("proxyBaseUrl:",s),console.log("serverRootPath:",e),e.length>0&&!a.endsWith(e)&&"/"!=e&&(a+=e,s=a),console.log("Updated proxyBaseUrl:",s)},u=()=>s||window.location.origin,h={GET:"GET",DELETE:"DELETE"},p="default_organization",g=0,m=async e=>{let t=Date.now();t-g>6e4?(e.includes("Authentication Error - Expired Key")&&(a.ZP.info("UI Session Expired. Logging out."),g=t,document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",window.location.href=window.location.pathname),g=t):console.log("Error suppressed to prevent spam:",e)},f="Authorization",w="x-mcp-auth";function y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Authorization";console.log("setGlobalLitellmHeaderName: ".concat(e)),f=e}let j=async(e,t)=>{let o=s?"".concat(s,"/model_group/make_public"):"/model_group/make_public";return(await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({model_groups:t})})).json()},_=async()=>{console.log("Getting UI config");let e=await fetch(c?"".concat(c,"/litellm/.well-known/litellm-ui-config"):"/litellm/.well-known/litellm-ui-config"),t=await e.json();return console.log("jsonData in getUiConfig:",t),d(t.server_root_path,t.proxy_base_url),t},C=async()=>{let e=await fetch(c?"".concat(c,"/public/model_hub/info"):"/public/model_hub/info");return await e.json()},k=async()=>{let e=s?"".concat(s,"/openapi.json"):"/openapi.json",t=await fetch(e);return await t.json()},v=async e=>{try{let t=s?"".concat(s,"/get/litellm_model_cost_map"):"/get/litellm_model_cost_map",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}}),a=await o.json();return console.log("received litellm model cost data: ".concat(a)),a}catch(e){throw console.error("Failed to get model cost map:",e),e}},T=async e=>{try{let t=s?"".concat(s,"/reload/model_cost_map"):"/reload/model_cost_map",o=await fetch(t,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}}),a=await o.json();return console.log("Model cost map reload response: ".concat(a)),a}catch(e){throw console.error("Failed to reload model cost map:",e),e}},E=async(e,t)=>{try{let o=s?"".concat(s,"/schedule/model_cost_map_reload?hours=").concat(t):"/schedule/model_cost_map_reload?hours=".concat(t),a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}}),r=await a.json();return console.log("Schedule model cost map reload response: ".concat(r)),r}catch(e){throw console.error("Failed to schedule model cost map reload:",e),e}},S=async e=>{try{let t=s?"".concat(s,"/schedule/model_cost_map_reload"):"/schedule/model_cost_map_reload",o=await fetch(t,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}}),a=await o.json();return console.log("Cancel model cost map reload response: ".concat(a)),a}catch(e){throw console.error("Failed to cancel model cost map reload:",e),e}},b=async e=>{try{let t=s?"".concat(s,"/schedule/model_cost_map_reload/status"):"/schedule/model_cost_map_reload/status";console.log("Fetching status from URL:",t);let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){console.error("Status request failed with status: ".concat(o.status));let e=await o.text();throw console.error("Error response:",e),Error("HTTP ".concat(o.status,": ").concat(e))}let a=await o.json();return console.log("Model cost map reload status:",a),a}catch(e){throw console.error("Failed to get model cost map reload status:",e),e}},F=async(e,t)=>{try{let o=s?"".concat(s,"/model/new"):"/model/new",r=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("API Response:",n),a.ZP.destroy(),a.ZP.success("Model ".concat(t.model_name," created successfully"),2),n}catch(e){throw console.error("Failed to create key:",e),e}},P=async e=>{try{let t=s?"".concat(s,"/model/settings"):"/model/settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){console.error("Failed to get model settings:",e)}},O=async(e,t)=>{console.log("model_id in model delete call: ".concat(t));try{let o=s?"".concat(s,"/model/delete"):"/model/delete",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},B=async(e,t)=>{if(console.log("budget_id in budget delete call: ".concat(t)),null!=e)try{let o=s?"".concat(s,"/budget/delete"):"/budget/delete",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},N=async(e,t)=>{try{console.log("Form Values in budgetCreateCall:",t),console.log("Form Values after check:",t);let o=s?"".concat(s,"/budget/new"):"/budget/new",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},x=async(e,t)=>{try{console.log("Form Values in budgetUpdateCall:",t),console.log("Form Values after check:",t);let o=s?"".concat(s,"/budget/update"):"/budget/update",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},G=async(e,t)=>{try{let o=s?"".concat(s,"/invitation/new"):"/invitation/new",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},J=async(e,t)=>{try{console.log("Form Values in invitationCreateCall:",t),console.log("Form Values after check:",t);let o=s?"".concat(s,"/invitation/claim"):"/invitation/claim",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},A=async e=>{try{let t=s?"".concat(s,"/alerting/settings"):"/alerting/settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},U=async(e,t)=>{try{for(let e of(console.log("Form Values in keyCreateServiceAccountCall:",t),t.description&&(t.metadata||(t.metadata={}),t.metadata.description=t.description,delete t.description,t.metadata=JSON.stringify(t.metadata)),r.d))if(t[e]){console.log("formValues.".concat(e,":"),t[e]);try{t[e]=JSON.parse(t[e])}catch(t){throw Error("Failed to parse ".concat(e,": ")+t)}}console.log("Form Values after check:",t);let o=s?"".concat(s,"/key/service-account/generate"):"/key/service-account/generate",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw m(e),console.error("Error response from the server:",e),Error(e)}let n=await a.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},R=async(e,t,o)=>{try{for(let e of(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),r.d))if(o[e]){console.log("formValues.".concat(e,":"),o[e]);try{o[e]=JSON.parse(o[e])}catch(t){throw Error("Failed to parse ".concat(e,": ")+t)}}console.log("Form Values after check:",o);let a=s?"".concat(s,"/key/generate"):"/key/generate",n=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!n.ok){let e=await n.text();throw m(e),console.error("Error response from the server:",e),Error(e)}let l=await n.json();return console.log("API Response:",l),l}catch(e){throw console.error("Failed to create key:",e),e}},I=async(e,t,o)=>{try{if(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),o.auto_create_key=!1,o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",o);let a=s?"".concat(s,"/user/new"):"/user/new",r=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!r.ok){let e=await r.text();throw m(e),console.error("Error response from the server:",e),Error(e)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},M=async(e,t)=>{try{let o=s?"".concat(s,"/key/delete"):"/key/delete";console.log("in keyDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:[t]})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},z=async(e,t)=>{try{let o=s?"".concat(s,"/user/delete"):"/user/delete";console.log("in userDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_ids:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete user(s):",e),e}},L=async(e,t)=>{try{let o=s?"".concat(s,"/team/delete"):"/team/delete";console.log("in teamDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_ids:[t]})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete key:",e),e}},D=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null,c=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,i=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let u=s?"".concat(s,"/user/list"):"/user/list";console.log("in userListCall");let h=new URLSearchParams;if(t&&t.length>0){let e=t.join(",");h.append("user_ids",e)}o&&h.append("page",o.toString()),a&&h.append("page_size",a.toString()),r&&h.append("user_email",r),n&&h.append("role",n),l&&h.append("team",l),c&&h.append("sso_user_ids",c),i&&h.append("sort_by",i),d&&h.append("sort_order",d);let p=h.toString();p&&(u+="?".concat(p));let g=await fetch(u,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!g.ok){let e=await g.json(),t=ov(e);throw m(t),Error(t)}let w=await g.json();return console.log("/user/list API Response:",w),w}catch(e){throw console.error("Failed to create key:",e),e}},V=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,n=arguments.length>5?arguments[5]:void 0,l=arguments.length>6&&void 0!==arguments[6]&&arguments[6];console.log("userInfoCall: ".concat(t,", ").concat(o,", ").concat(a,", ").concat(r,", ").concat(n,", ").concat(l));try{let c;if(a){c=s?"".concat(s,"/user/list"):"/user/list";let e=new URLSearchParams;null!=r&&e.append("page",r.toString()),null!=n&&e.append("page_size",n.toString()),c+="?".concat(e.toString())}else c=s?"".concat(s,"/user/info"):"/user/info",("Admin"!==o&&"Admin Viewer"!==o||l)&&t&&(c+="?user_id=".concat(t));console.log("Requesting user data from:",c);let i=await fetch(c,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=ov(e);throw m(t),Error(t)}let d=await i.json();return console.log("API Response:",d),d}catch(e){throw console.error("Failed to fetch user data:",e),e}},q=async(e,t)=>{try{let o=s?"".concat(s,"/team/info"):"/team/info";t&&(o="".concat(o,"?team_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},Z=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6],arguments.length>7&&void 0!==arguments[7]&&arguments[7],arguments.length>8&&void 0!==arguments[8]&&arguments[8];try{let n=s?"".concat(s,"/v2/team/list"):"/v2/team/list";console.log("in teamInfoCall");let l=new URLSearchParams;o&&l.append("user_id",o.toString()),t&&l.append("organization_id",t.toString()),a&&l.append("team_id",a.toString()),r&&l.append("team_alias",r.toString());let c=l.toString();c&&(n+="?".concat(c));let i=await fetch(n,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=ov(e);throw m(t),Error(t)}let d=await i.json();return console.log("/v2/team/list API Response:",d),d}catch(e){throw console.error("Failed to create key:",e),e}},H=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=s?"".concat(s,"/team/list"):"/team/list";console.log("in teamInfoCall");let l=new URLSearchParams;o&&l.append("user_id",o.toString()),t&&l.append("organization_id",t.toString()),a&&l.append("team_id",a.toString()),r&&l.append("team_alias",r.toString());let c=l.toString();c&&(n+="?".concat(c));let i=await fetch(n,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=ov(e);throw m(t),Error(t)}let d=await i.json();return console.log("/team/list API Response:",d),d}catch(e){throw console.error("Failed to create key:",e),e}},W=async e=>{try{let t=s?"".concat(s,"/team/available"):"/team/available";console.log("in availableTeamListCall");let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("/team/available_teams API Response:",a),a}catch(e){throw e}},Y=async e=>{try{let t=s?"".concat(s,"/organization/list"):"/organization/list",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},K=async(e,t)=>{try{let o=s?"".concat(s,"/organization/info"):"/organization/info";t&&(o="".concat(o,"?organization_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},$=async(e,t)=>{try{if(console.log("Form Values in organizationCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw console.error("Failed to parse metadata:",e),Error("Failed to parse metadata: "+e)}}let o=s?"".concat(s,"/organization/new"):"/organization/new",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},Q=async(e,t)=>{try{console.log("Form Values in organizationUpdateCall:",t);let o=s?"".concat(s,"/organization/update"):"/organization/update",a=await fetch(o,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("Update Team Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},X=async(e,t)=>{try{let o=s?"".concat(s,"/organization/delete"):"/organization/delete",a=await fetch(o,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_ids:[t]})});if(!a.ok){let e=await a.text();throw m(e),Error("Error deleting organization: ".concat(e))}return await a.json()}catch(e){throw console.error("Failed to delete organization:",e),e}},ee=async(e,t)=>{try{let o=s?"".concat(s,"/utils/transform_request"):"/utils/transform_request",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},et=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;try{let r=s?"".concat(s,"/user/daily/activity"):"/user/daily/activity",n=new URLSearchParams;n.append("start_date",l(t)),n.append("end_date",l(o)),n.append("page_size","1000"),n.append("page",a.toString());let c=n.toString();c&&(r+="?".concat(c));let i=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=ov(e);throw m(t),Error(t)}return await i.json()}catch(e){throw console.error("Failed to create key:",e),e}},eo=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=s?"".concat(s,"/tag/daily/activity"):"/tag/daily/activity",c=new URLSearchParams;c.append("start_date",l(t)),c.append("end_date",l(o)),c.append("page_size","1000"),c.append("page",a.toString()),r&&c.append("tags",r.join(","));let i=c.toString();i&&(n+="?".concat(i));let d=await fetch(n,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!d.ok){let e=await d.json(),t=ov(e);throw m(t),Error(t)}return await d.json()}catch(e){throw console.error("Failed to create key:",e),e}},ea=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=s?"".concat(s,"/team/daily/activity"):"/team/daily/activity",c=new URLSearchParams;c.append("start_date",l(t)),c.append("end_date",l(o)),c.append("page_size","1000"),c.append("page",a.toString()),r&&c.append("team_ids",r.join(",")),c.append("exclude_team_ids","litellm-dashboard");let i=c.toString();i&&(n+="?".concat(i));let d=await fetch(n,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!d.ok){let e=await d.json(),t=ov(e);throw m(t),Error(t)}return await d.json()}catch(e){throw console.error("Failed to create key:",e),e}},er=async e=>{try{let t=s?"".concat(s,"/global/spend"):"/global/spend",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},en=async e=>{try{let t=s?"".concat(s,"/onboarding/get_token"):"/onboarding/get_token";t+="?invite_link=".concat(e);let o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},el=async(e,t,o,a)=>{let r=s?"".concat(s,"/onboarding/claim_token"):"/onboarding/claim_token";try{let n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({invitation_link:t,user_id:o,password:a})});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to delete key:",e),e}},ec=async(e,t,o)=>{try{let a=s?"".concat(s,"/key/").concat(t,"/regenerate"):"/key/".concat(t,"/regenerate"),r=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("Regenerate key Response:",n),n}catch(e){throw console.error("Failed to regenerate key:",e),e}},ei=!1,es=null,ed=async(e,t,o)=>{try{console.log("modelInfoCall:",e,t,o);let r=s?"".concat(s,"/v2/model/info"):"/v2/model/info",n=new URLSearchParams;n.append("include_team_models","true"),n.toString()&&(r+="?".concat(n.toString()));let l=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!l.ok){let e=await l.text();throw e+="error shown=".concat(ei),ei||(e.includes("No model list passed")&&(e="No Models Exist. Click Add Model to get started."),a.ZP.info(e,10),ei=!0,es&&clearTimeout(es),es=setTimeout(()=>{ei=!1},1e4)),Error("Network response was not ok")}let c=await l.json();return console.log("modelInfoCall:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},eu=async(e,t)=>{try{let o=s?"".concat(s,"/v1/model/info"):"/v1/model/info";o+="?litellm_model_id=".concat(t);let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("modelInfoV1Call:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eh=async()=>{let e=s?"".concat(s,"/public/model_hub"):"/public/model_hub";return(await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}})).json()},ep=async e=>{try{let t=s?"".concat(s,"/model_group/info"):"/model_group/info",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("modelHubCall:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},eg=async e=>{try{let t=s?"".concat(s,"/get/allowed_ips"):"/get/allowed_ips",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("getAllowedIPs:",a),a.data}catch(e){throw console.error("Failed to get allowed IPs:",e),e}},em=async(e,t)=>{try{let o=s?"".concat(s,"/add/allowed_ip"):"/add/allowed_ip",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("addAllowedIP:",r),r}catch(e){throw console.error("Failed to add allowed IP:",e),e}},ef=async(e,t)=>{try{let o=s?"".concat(s,"/delete/allowed_ip"):"/delete/allowed_ip",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("deleteAllowedIP:",r),r}catch(e){throw console.error("Failed to delete allowed IP:",e),e}},ew=async(e,t,o,a,r,n,l,c)=>{try{let t=s?"".concat(s,"/model/metrics"):"/model/metrics";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(l,"&customer=").concat(c));let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},ey=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/model/streaming_metrics"):"/model/streaming_metrics";t&&(r="".concat(r,"?_selected_model_group=").concat(t,"&startTime=").concat(o,"&endTime=").concat(a));let n=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}return await n.json()}catch(e){throw console.error("Failed to create key:",e),e}},ej=async(e,t,o,a,r,n,l,c)=>{try{let t=s?"".concat(s,"/model/metrics/slow_responses"):"/model/metrics/slow_responses";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(l,"&customer=").concat(c));let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},e_=async(e,t,o,a,r,n,l,c)=>{try{let t=s?"".concat(s,"/model/metrics/exceptions"):"/model/metrics/exceptions";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(l,"&customer=").concat(c));let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},eC=async(e,t)=>{try{let o=s?"".concat(s,"/model_hub/update_useful_links"):"/model_hub/update_useful_links",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({useful_links:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},ek=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=(arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6]);console.log("in /models calls, globalLitellmHeaderName",f);try{let t=s?"".concat(s,"/models"):"/models",o=new URLSearchParams;o.append("include_model_access_groups","True"),!0===a&&o.append("return_wildcard_routes","True"),!0===n&&o.append("only_model_access_groups","True"),r&&o.append("team_id",r.toString()),o.toString()&&(t+="?".concat(o.toString()));let l=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}return await l.json()}catch(e){throw console.error("Failed to create key:",e),e}},ev=async(e,t)=>{try{let o=s?"".concat(s,"/global/spend/logs"):"/global/spend/logs";console.log("in keySpendLogsCall:",o);let a=await fetch("".concat(o,"?api_key=").concat(t),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},eT=async e=>{try{let t=s?"".concat(s,"/global/spend/teams"):"/global/spend/teams";console.log("in teamSpendLogsCall:",t);let o=await fetch("".concat(t),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eE=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/global/spend/tags"):"/global/spend/tags";t&&o&&(r="".concat(r,"?start_date=").concat(t,"&end_date=").concat(o)),a&&(r+="".concat(r,"&tags=").concat(a.join(","))),console.log("in tagsSpendLogsCall:",r);let n=await fetch("".concat(r),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to create key:",e),e}},eS=async e=>{try{let t=s?"".concat(s,"/global/spend/all_tag_names"):"/global/spend/all_tag_names";console.log("in global/spend/all_tag_names call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eb=async e=>{try{let t=s?"".concat(s,"/global/all_end_users"):"/global/all_end_users";console.log("in global/all_end_users call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eF=async(e,t)=>{try{let o=s?"".concat(s,"/user/filter/ui"):"/user/filter/ui";t.get("user_email")&&(o+="?user_email=".concat(t.get("user_email"))),t.get("user_id")&&(o+="?user_id=".concat(t.get("user_id")));let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},eP=async(e,t,o,a,r,n)=>{try{console.log("user role in spend logs call: ".concat(o));let t=s?"".concat(s,"/spend/logs"):"/spend/logs";t="App Owner"==o?"".concat(t,"?user_id=").concat(a,"&start_date=").concat(r,"&end_date=").concat(n):"".concat(t,"?start_date=").concat(r,"&end_date=").concat(n);let l=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}let c=await l.json();return console.log(c),c}catch(e){throw console.error("Failed to create key:",e),e}},eO=async(e,t,o,a,r,n,l,c,i,d,u,h)=>{try{let p=s?"".concat(s,"/spend/logs/ui"):"/spend/logs/ui",g=new URLSearchParams;t&&g.append("api_key",t),o&&g.append("team_id",o),a&&g.append("request_id",a),r&&g.append("start_date",r),n&&g.append("end_date",n),l&&g.append("page",l.toString()),c&&g.append("page_size",c.toString()),i&&g.append("user_id",i),d&&g.append("end_user",d),u&&g.append("status_filter",u),h&&g.append("model",h);let w=g.toString();w&&(p+="?".concat(w));let y=await fetch(p,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!y.ok){let e=await y.json(),t=ov(e);throw m(t),Error(t)}let j=await y.json();return console.log("Spend Logs Response:",j),j}catch(e){throw console.error("Failed to fetch spend logs:",e),e}},eB=async e=>{try{let t=s?"".concat(s,"/global/spend/logs"):"/global/spend/logs",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eN=async e=>{try{let t=s?"".concat(s,"/global/spend/keys?limit=5"):"/global/spend/keys?limit=5",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ex=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/global/spend/end_users"):"/global/spend/end_users",n="";n=t?JSON.stringify({api_key:t,startTime:o,endTime:a}):JSON.stringify({startTime:o,endTime:a});let l={method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:n},c=await fetch(r,l);if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}let i=await c.json();return console.log(i),i}catch(e){throw console.error("Failed to create key:",e),e}},eG=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/global/spend/provider"):"/global/spend/provider";o&&a&&(r+="?start_date=".concat(o,"&end_date=").concat(a)),t&&(r+="&api_key=".concat(t));let n={method:"GET",headers:{[f]:"Bearer ".concat(e)}},l=await fetch(r,n);if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}let c=await l.json();return console.log(c),c}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eJ=async(e,t,o)=>{try{let a=s?"".concat(s,"/global/activity"):"/global/activity";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[f]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eA=async(e,t,o)=>{try{let a=s?"".concat(s,"/global/activity/cache_hits"):"/global/activity/cache_hits";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[f]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eU=async(e,t,o)=>{try{let a=s?"".concat(s,"/global/activity/model"):"/global/activity/model";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[f]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eR=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/global/activity/exceptions"):"/global/activity/exceptions";t&&o&&(r+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(r+="&model_group=".concat(a));let n={method:"GET",headers:{[f]:"Bearer ".concat(e)}},l=await fetch(r,n);if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}let c=await l.json();return console.log(c),c}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eI=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/global/activity/exceptions/deployment"):"/global/activity/exceptions/deployment";t&&o&&(r+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(r+="&model_group=".concat(a));let n={method:"GET",headers:{[f]:"Bearer ".concat(e)}},l=await fetch(r,n);if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}let c=await l.json();return console.log(c),c}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eM=async e=>{try{let t=s?"".concat(s,"/global/spend/models?limit=5"):"/global/spend/models?limit=5",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ez=async(e,t)=>{try{let o=s?"".concat(s,"/v2/key/info"):"/v2/key/info",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:t})});if(!a.ok){let e=await a.text();if(e.includes("Invalid proxy server token passed"))throw Error("Invalid proxy server token passed");throw m(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},eL=async(e,t,o)=>{try{console.log("Sending model connection test request:",JSON.stringify(t));let r=s?"".concat(s,"/health/test_connection"):"/health/test_connection",n=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json",[f]:"Bearer ".concat(e)},body:JSON.stringify({litellm_params:t,mode:o})}),l=n.headers.get("content-type");if(!l||!l.includes("application/json")){let e=await n.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(n.status,": ").concat(n.statusText,"). Check network tab for details."))}let c=await n.json();if(!n.ok||"error"===c.status){if("error"===c.status);else{var a;return{status:"error",message:(null===(a=c.error)||void 0===a?void 0:a.message)||"Connection test failed: ".concat(n.status," ").concat(n.statusText)}}}return c}catch(e){throw console.error("Model connection test error:",e),e}},eD=async(e,t)=>{try{console.log("entering keyInfoV1Call");let o=s?"".concat(s,"/key/info"):"/key/info";o="".concat(o,"?key=").concat(t);let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(console.log("response",a),!a.ok){let e=await a.text();m(e),n.Z.fromBackend("Failed to fetch key info - "+e)}let r=await a.json();return console.log("data",r),r}catch(e){throw console.error("Failed to fetch key info:",e),e}},eV=async function(e,t,o,a,r,n,l,c){let i=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let u=s?"".concat(s,"/key/list"):"/key/list";console.log("in keyListCall");let h=new URLSearchParams;o&&h.append("team_id",o.toString()),t&&h.append("organization_id",t.toString()),a&&h.append("key_alias",a),n&&h.append("key_hash",n),r&&h.append("user_id",r.toString()),l&&h.append("page",l.toString()),c&&h.append("size",c.toString()),i&&h.append("sort_by",i),d&&h.append("sort_order",d),h.append("return_full_object","true"),h.append("include_team_keys","true");let p=h.toString();p&&(u+="?".concat(p));let g=await fetch(u,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!g.ok){let e=await g.json(),t=ov(e);throw m(t),Error(t)}let w=await g.json();return console.log("/team/list API Response:",w),w}catch(e){throw console.error("Failed to create key:",e),e}},eq=async(e,t)=>{try{let o=s?"".concat(s,"/spend/users"):"/spend/users";console.log("in spendUsersCall:",o);let a=await fetch("".concat(o,"?user_id=").concat(t),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to get spend for user",e),e}},eZ=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/user/request_model"):"/user/request_model",n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({models:[t],user_id:o,justification:a})});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log(l),l}catch(e){throw console.error("Failed to create key:",e),e}},eH=async e=>{try{let t=s?"".concat(s,"/user/get_requests"):"/user/get_requests";console.log("in userGetRequesedtModelsCall:",t);let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to get requested models:",e),e}},eW=async(e,t,o)=>{try{let a=s?"".concat(s,"/user/daily/activity/aggregated"):"/user/daily/activity/aggregated",r=new URLSearchParams,n=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};r.append("start_date",n(t)),r.append("end_date",n(o));let l=r.toString();l&&(a+="?".concat(l));let c=await fetch(a,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}return await c.json()}catch(e){throw console.error("Failed to fetch aggregated user daily activity:",e),e}},eY=async(e,t)=>{try{let o=s?"".concat(s,"/user/get_users?role=").concat(t):"/user/get_users?role=".concat(t);console.log("in userGetAllUsersCall:",o);let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to get requested models:",e),e}},eK=async e=>{try{let t=s?"".concat(s,"/user/available_roles"):"/user/available_roles",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("response from user/available_role",a),a}catch(e){throw e}},e$=async(e,t)=>{try{if(console.log("Form Values in teamCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=s?"".concat(s,"/team/new"):"/team/new",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eQ=async(e,t)=>{try{if(console.log("Form Values in credentialCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=s?"".concat(s,"/credentials"):"/credentials",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eX=async e=>{try{let t=s?"".concat(s,"/credentials"):"/credentials";console.log("in credentialListCall");let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("/credentials API Response:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},e0=async(e,t,o)=>{try{let a=s?"".concat(s,"/credentials"):"/credentials";t?a+="/by_name/".concat(t):o&&(a+="/by_model/".concat(o)),console.log("in credentialListCall");let r=await fetch(a,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("/credentials API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},e1=async(e,t)=>{try{let o=s?"".concat(s,"/credentials/").concat(t):"/credentials/".concat(t);console.log("in credentialDeleteCall:",t);let a=await fetch(o,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete key:",e),e}},e2=async(e,t,o)=>{try{if(console.log("Form Values in credentialUpdateCall:",o),o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let a=s?"".concat(s,"/credentials/").concat(t):"/credentials/".concat(t),r=await fetch(a,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...o})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},e4=async(e,t)=>{try{if(console.log("Form Values in keyUpdateCall:",t),t.model_tpm_limit){console.log("formValues.model_tpm_limit:",t.model_tpm_limit);try{t.model_tpm_limit=JSON.parse(t.model_tpm_limit)}catch(e){throw Error("Failed to parse model_tpm_limit: "+e)}}if(t.model_rpm_limit){console.log("formValues.model_rpm_limit:",t.model_rpm_limit);try{t.model_rpm_limit=JSON.parse(t.model_rpm_limit)}catch(e){throw Error("Failed to parse model_rpm_limit: "+e)}}let o=s?"".concat(s,"/key/update"):"/key/update",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw m(e),console.error("Error response from the server:",e),Error(e)}let r=await a.json();return console.log("Update key Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},e3=async(e,t)=>{try{console.log("Form Values in teamUpateCall:",t);let o=s?"".concat(s,"/team/update"):"/team/update",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw m(e),console.error("Error response from the server:",e),n.Z.fromBackend("Failed to update team settings: "+e),Error(e)}let r=await a.json();return console.log("Update Team Response:",r),r}catch(e){throw console.error("Failed to update team:",e),e}},e5=async(e,t,o)=>{try{console.log("Form Values in modelUpateCall:",t);let a=s?"".concat(s,"/model/").concat(o,"/update"):"/model/".concat(o,"/update"),r=await fetch(a,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!r.ok){let e=await r.text();throw m(e),console.error("Error update from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("Update model Response:",n),n}catch(e){throw console.error("Failed to update model:",e),e}},e6=async(e,t)=>{try{console.log("Form Values in modelUpateCall:",t);let o=s?"".concat(s,"/model/update"):"/model/update",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw m(e),console.error("Error update from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("Update model Response:",r),r}catch(e){throw console.error("Failed to update model:",e),e}},e7=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let r=s?"".concat(s,"/team/member_add"):"/team/member_add",n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,member:o})});if(!n.ok){var a;let e=await n.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let l=await n.json();return console.log("API Response:",l),l}catch(e){throw console.error("Failed to create key:",e),e}},e9=async(e,t,o,a,r)=>{try{console.log("Bulk add team members:",{teamId:t,members:o,maxBudgetInTeam:a});let l=s?"".concat(s,"/team/bulk_member_add"):"/team/bulk_member_add",c={team_id:t};r?c.all_users=!0:c.members=o,null!=a&&(c.max_budget_in_team=a);let i=await fetch(l,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(c)});if(!i.ok){var n;let e=await i.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(n=t.detail)||void 0===n?void 0:n.error)||"Failed to bulk add team members",a=Error(o);throw a.raw=t,a}let d=await i.json();return console.log("Bulk team member add API Response:",d),d}catch(e){throw console.error("Failed to bulk add team members:",e),e}},e8=async(e,t,o)=>{try{console.log("Form Values in teamMemberUpdateCall:",o),console.log("Budget value:",o.max_budget_in_team),console.log("TPM limit:",o.tpm_limit),console.log("RPM limit:",o.rpm_limit);let r=s?"".concat(s,"/team/member_update"):"/team/member_update",n={team_id:t,role:o.role,user_id:o.user_id};void 0!==o.user_email&&(n.user_email=o.user_email),void 0!==o.max_budget_in_team&&null!==o.max_budget_in_team&&(n.max_budget_in_team=o.max_budget_in_team),void 0!==o.tpm_limit&&null!==o.tpm_limit&&(n.tpm_limit=o.tpm_limit),void 0!==o.rpm_limit&&null!==o.rpm_limit&&(n.rpm_limit=o.rpm_limit),console.log("Final request body:",n);let l=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(n)});if(!l.ok){var a;let e=await l.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let c=await l.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to update team member:",e),e}},te=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=s?"".concat(s,"/team/member_delete"):"/team/member_delete",r=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,...void 0!==o.user_email&&{user_email:o.user_email},...void 0!==o.user_id&&{user_id:o.user_id}})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},tt=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=s?"".concat(s,"/organization/member_add"):"/organization/member_add",r=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,member:o})});if(!r.ok){let e=await r.text();throw m(e),console.error("Error response from the server:",e),Error(e)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create organization member:",e),e}},to=async(e,t,o)=>{try{console.log("Form Values in organizationMemberDeleteCall:",o);let a=s?"".concat(s,"/organization/member_delete"):"/organization/member_delete",r=await fetch(a,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,user_id:o})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to delete organization member:",e),e}},ta=async(e,t,o)=>{try{console.log("Form Values in organizationMemberUpdateCall:",o);let a=s?"".concat(s,"/organization/member_update"):"/organization/member_update",r=await fetch(a,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,...o})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to update organization member:",e),e}},tr=async(e,t,o)=>{try{console.log("Form Values in userUpdateUserCall:",t);let a=s?"".concat(s,"/user/update"):"/user/update",r={...t};null!==o&&(r.user_role=o),r=JSON.stringify(r);let n=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:r});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return console.log("API Response:",l),l}catch(e){throw console.error("Failed to create key:",e),e}},tn=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];try{let r;console.log("Form Values in userUpdateUserCall:",t);let n=s?"".concat(s,"/user/bulk_update"):"/user/bulk_update";if(a)r=JSON.stringify({all_users:!0,user_updates:t});else if(o&&o.length>0){let e=[];for(let a of o)e.push({user_id:a,...t});r=JSON.stringify({users:e})}else throw Error("Must provide either userIds or set allUsers=true");let l=await fetch(n,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:r});if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}let c=await l.json();return console.log("API Response:",c),c}catch(e){throw console.error("Failed to create key:",e),e}},tl=async(e,t)=>{try{let o=s?"".concat(s,"/global/predict/spend/logs"):"/global/predict/spend/logs",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({data:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},tc=async e=>{try{let t=s?"".concat(s,"/health/services?service=slack_budget_alerts"):"/health/services?service=slack_budget_alerts";console.log("Checking Slack Budget Alerts service health");let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error(e)}let r=await o.json();return a.ZP.success("Test Slack Alert worked - check your Slack!"),console.log("Service Health Response:",r),r}catch(e){throw console.error("Failed to perform health check:",e),e}},ti=async(e,t)=>{try{let o=s?"".concat(s,"/health/services?service=").concat(t):"/health/services?service=".concat(t);console.log("Checking Slack Budget Alerts service health");let a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw m(e),Error(e)}return await a.json()}catch(e){throw console.error("Failed to perform health check:",e),e}},ts=async e=>{try{let t=s?"".concat(s,"/budget/list"):"/budget/list",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},td=async e=>{try{let t=s?"".concat(s,"/budget/settings"):"/budget/settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tu=async(e,t,o)=>{try{let t=s?"".concat(s,"/get/config/callbacks"):"/get/config/callbacks",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},th=async e=>{try{let t=s?"".concat(s,"/config/list?config_type=general_settings"):"/config/list?config_type=general_settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tp=async e=>{try{let t=s?"".concat(s,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tg=async(e,t)=>{try{let o=s?"".concat(s,"/config/field/info?field_name=").concat(t):"/config/field/info?field_name=".concat(t),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tm=async(e,t,o)=>{try{let r=s?"".concat(s,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,field_value:o})});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return a.ZP.success("Successfully updated value!"),l}catch(e){throw console.error("Failed to set callbacks:",e),e}},tf=async(e,t)=>{try{let o=s?"".concat(s,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tw=async(e,t,o)=>{try{let r=s?"".concat(s,"/config/field/update"):"/config/field/update",n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,field_value:o,config_type:"general_settings"})});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return a.ZP.success("Successfully updated value!"),l}catch(e){throw console.error("Failed to set callbacks:",e),e}},ty=async(e,t)=>{try{let o=s?"".concat(s,"/config/field/delete"):"/config/field/delete",r=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,config_type:"general_settings"})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return a.ZP.success("Field reset on proxy"),n}catch(e){throw console.error("Failed to get callbacks:",e),e}},tj=async(e,t)=>{try{let o=s?"".concat(s,"/config/pass_through_endpoint?endpoint_id=").concat(t):"/config/pass_through_endpoint".concat(t),a=await fetch(o,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},t_=async(e,t)=>{try{let o=s?"".concat(s,"/config/update"):"/config/update",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tC=async e=>{try{let t=s?"".concat(s,"/health"):"/health",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to call /health:",e),e}},tk=async(e,t)=>{try{let o=s?"".concat(s,"/health?model=").concat(encodeURIComponent(t)):"/health?model=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to call /health for model ".concat(t,":"),e),e}},tv=async e=>{try{let t=s?"".concat(s,"/cache/ping"):"/cache/ping",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /cache/ping:",e),e}},tT=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;try{let n=s?"".concat(s,"/health/history"):"/health/history",l=new URLSearchParams;t&&l.append("model",t),o&&l.append("status_filter",o),l.append("limit",a.toString()),l.append("offset",r.toString()),l.toString()&&(n+="?".concat(l.toString()));let c=await fetch(n,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.text();throw m(e),Error(e)}return await c.json()}catch(e){throw console.error("Failed to call /health/history:",e),e}},tE=async e=>{try{let t=s?"".concat(s,"/health/latest"):"/health/latest",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /health/latest:",e),e}},tS=async e=>{try{console.log("Getting proxy UI settings"),console.log("proxyBaseUrl in getProxyUISettings:",s);let t=s?"".concat(s,"/sso/get/ui_settings"):"/sso/get/ui_settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tb=async e=>{try{let t=s?"".concat(s,"/v2/guardrails/list"):"/v2/guardrails/list",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get guardrails list:",e),e}},tF=async e=>{try{let t=s?"".concat(s,"/prompts/list"):"/prompts/list",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to get prompts list:",e),e}},tP=async(e,t)=>{try{let o=s?"".concat(s,"/prompts/").concat(t,"/info"):"/prompts/".concat(t,"/info"),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to get prompt info:",e),e}},tO=async(e,t)=>{try{let o=s?"".concat(s,"/prompts"):"/prompts",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to create prompt:",e),e}},tB=async(e,t,o)=>{try{let a=s?"".concat(s,"/prompts/").concat(t):"/prompts/".concat(t),r=await fetch(a,{method:"PUT",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}return await r.json()}catch(e){throw console.error("Failed to update prompt:",e),e}},tN=async(e,t)=>{try{let o=s?"".concat(s,"/prompts/").concat(t):"/prompts/".concat(t),a=await fetch(o,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to delete prompt:",e),e}},tx=async(e,t)=>{try{let o=new FormData;o.append("file",t);let a=s?"".concat(s,"/utils/dotprompt_json_converter"):"/utils/dotprompt_json_converter",r=await fetch(a,{method:"POST",headers:{[f]:"Bearer ".concat(e)},body:o});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}return await r.json()}catch(e){throw console.error("Failed to convert prompt file:",e),e}},tG=async(e,t,o)=>{try{let a=s?"".concat(s,"/prompts/").concat(t):"/prompts/".concat(t),r=await fetch(a,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}return await r.json()}catch(e){throw console.error("Failed to patch prompt:",e),e}},tJ=async(e,t)=>{try{let o=s?"".concat(s,"/guardrails"):"/guardrails",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({guardrail:t})});if(!a.ok){let e=await a.text();throw m(e),Error(e)}let r=await a.json();return console.log("Create guardrail response:",r),r}catch(e){throw console.error("Failed to create guardrail:",e),e}},tA=async(e,t,o)=>{try{let a=s?"".concat(s,"/spend/logs/ui/").concat(t,"?start_date=").concat(encodeURIComponent(o)):"/spend/logs/ui/".concat(t,"?start_date=").concat(encodeURIComponent(o));console.log("Fetching log details from:",a);let r=await fetch(a,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("Fetched log details:",n),n}catch(e){throw console.error("Failed to fetch log details:",e),e}},tU=async e=>{try{let t=s?"".concat(s,"/get/internal_user_settings"):"/get/internal_user_settings";console.log("Fetching SSO settings from:",t);let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("Fetched SSO settings:",a),a}catch(e){throw console.error("Failed to fetch SSO settings:",e),e}},tR=async(e,t)=>{try{let o=s?"".concat(s,"/update/internal_user_settings"):"/update/internal_user_settings";console.log("Updating internal user settings:",t);let r=await fetch(o,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.text();throw m(e),Error(e)}let n=await r.json();return console.log("Updated internal user settings:",n),a.ZP.success("Internal user settings updated successfully"),n}catch(e){throw console.error("Failed to update internal user settings:",e),e}},tI=async e=>{try{let t=s?"".concat(s,"/v1/mcp/server"):"/v1/mcp/server";console.log("Fetching MCP servers from:",t);let o=await fetch(t,{method:h.GET,headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("Fetched MCP servers:",a),a}catch(e){throw console.error("Failed to fetch MCP servers:",e),e}},tM=async e=>{try{let t=s?"".concat(s,"/v1/mcp/access_groups"):"/v1/mcp/access_groups";console.log("Fetching MCP access groups from:",t);let o=await fetch(t,{method:h.GET,headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("Fetched MCP access groups:",a),a.access_groups||[]}catch(e){throw console.error("Failed to fetch MCP access groups:",e),e}},tz=async(e,t)=>{try{console.log("Form Values in createMCPServer:",t);let o=s?"".concat(s,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},tL=async(e,t)=>{try{let o=s?"".concat(s,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"PUT",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to update MCP server:",e),e}},tD=async(e,t)=>{try{let o=(s?"".concat(s):"")+"/v1/mcp/server/".concat(t);console.log("in deleteMCPServer:",t);let a=await fetch(o,{method:h.DELETE,headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}}catch(e){throw console.error("Failed to delete key:",e),e}},tV=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/mcp-rest/tools/list?server_id=").concat(t):"/mcp-rest/tools/list?server_id=".concat(t);console.log("Fetching MCP tools from:",r);let n={[f]:"Bearer ".concat(e),"Content-Type":"application/json"};a&&o?n["x-mcp-".concat(a,"-authorization")]=o:o&&(n[w]=o);let l=await fetch(r,{method:"GET",headers:n}),c=await l.json();if(console.log("Fetched MCP tools response:",c),!l.ok){if(c.error&&c.message)throw Error(c.message);throw Error("Failed to fetch MCP tools")}return c}catch(e){return console.error("Failed to fetch MCP tools:",e),{tools:[],error:"network_error",message:e instanceof Error?e.message:"Failed to fetch MCP tools"}}},tq=async(e,t,o,a,r)=>{try{let n=s?"".concat(s,"/mcp-rest/tools/call"):"/mcp-rest/tools/call";console.log("Calling MCP tool:",t,"with arguments:",o);let l={[f]:"Bearer ".concat(e),"Content-Type":"application/json"};r?l["x-mcp-".concat(r,"-authorization")]=a:l[w]=a;let c=await fetch(n,{method:"POST",headers:l,body:JSON.stringify({name:t,arguments:o})});if(!c.ok){let e="Network response was not ok",t=null,o=await c.text();try{let a=JSON.parse(o);a.detail?"string"==typeof a.detail?e=a.detail:"object"==typeof a.detail&&(e=a.detail.message||a.detail.error||"An error occurred",t=a.detail):e=a.message||a.error||e}catch(t){console.error("Failed to parse JSON error response:",t),o&&(e=o)}let a=Error(e);throw a.status=c.status,a.statusText=c.statusText,a.details=t,m(e),a}let i=await c.json();return console.log("MCP tool call response:",i),i}catch(e){throw console.error("Failed to call MCP tool:",e),console.error("Error type:",typeof e),e instanceof Error&&(console.error("Error message:",e.message),console.error("Error stack:",e.stack)),e}},tZ=async(e,t)=>{try{let o=s?"".concat(s,"/tag/new"):"/tag/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await m(e);return}return await a.json()}catch(e){throw console.error("Error creating tag:",e),e}},tH=async(e,t)=>{try{let o=s?"".concat(s,"/tag/update"):"/tag/update",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await m(e);return}return await a.json()}catch(e){throw console.error("Error updating tag:",e),e}},tW=async(e,t)=>{try{let o=s?"".concat(s,"/tag/info"):"/tag/info",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({names:t})});if(!a.ok){let e=await a.text();return await m(e),{}}return await a.json()}catch(e){throw console.error("Error getting tag info:",e),e}},tY=async e=>{try{let t=s?"".concat(s,"/tag/list"):"/tag/list",o=await fetch(t,{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.text();return await m(e),{}}return await o.json()}catch(e){throw console.error("Error listing tags:",e),e}},tK=async(e,t)=>{try{let o=s?"".concat(s,"/tag/delete"):"/tag/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({name:t})});if(!a.ok){let e=await a.text();await m(e);return}return await a.json()}catch(e){throw console.error("Error deleting tag:",e),e}},t$=async e=>{try{let t=s?"".concat(s,"/get/default_team_settings"):"/get/default_team_settings";console.log("Fetching default team settings from:",t);let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("Fetched default team settings:",a),a}catch(e){throw console.error("Failed to fetch default team settings:",e),e}},tQ=async(e,t)=>{try{let o=s?"".concat(s,"/update/default_team_settings"):"/update/default_team_settings";console.log("Updating default team settings:",t);let r=await fetch(o,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("Updated default team settings:",n),a.ZP.success("Default team settings updated successfully"),n}catch(e){throw console.error("Failed to update default team settings:",e),e}},tX=async(e,t)=>{try{let o=s?"".concat(s,"/team/permissions_list?team_id=").concat(t):"/team/permissions_list?team_id=".concat(t),a=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("Team permissions response:",r),r}catch(e){throw console.error("Failed to get team permissions:",e),e}},t0=async(e,t,o)=>{try{let a=s?"".concat(s,"/team/permissions_update"):"/team/permissions_update",r=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({team_id:t,team_member_permissions:o})});if(!r.ok){let e=await r.json(),t=ov(e);throw m(t),Error(t)}let n=await r.json();return console.log("Team permissions response:",n),n}catch(e){throw console.error("Failed to update team permissions:",e),e}},t1=async(e,t)=>{try{let o=s?"".concat(s,"/spend/logs/session/ui?session_id=").concat(encodeURIComponent(t)):"/spend/logs/session/ui?session_id=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to fetch session logs:",e),e}},t2=async(e,t)=>{try{let o=s?"".concat(s,"/vector_store/new"):"/vector_store/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to create vector store")}return await a.json()}catch(e){throw console.error("Error creating vector store:",e),e}},t4=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let t=s?"".concat(s,"/vector_store/list"):"/vector_store/list",o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.json();throw Error(e.detail||"Failed to list vector stores")}return await o.json()}catch(e){throw console.error("Error listing vector stores:",e),e}},t3=async(e,t)=>{try{let o=s?"".concat(s,"/vector_store/delete"):"/vector_store/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({vector_store_id:t})});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to delete vector store")}return await a.json()}catch(e){throw console.error("Error deleting vector store:",e),e}},t5=async(e,t)=>{try{let o=s?"".concat(s,"/vector_store/info"):"/vector_store/info",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({vector_store_id:t})});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to get vector store info")}return await a.json()}catch(e){throw console.error("Error getting vector store info:",e),e}},t6=async(e,t)=>{try{let o=s?"".concat(s,"/vector_store/update"):"/vector_store/update",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to update vector store")}return await a.json()}catch(e){throw console.error("Error updating vector store:",e),e}},t7=async e=>{try{let t=s?"".concat(s,"/email/event_settings"):"/email/event_settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error("Failed to get email event settings")}let a=await o.json();return console.log("Email event settings response:",a),a}catch(e){throw console.error("Failed to get email event settings:",e),e}},t9=async(e,t)=>{try{let o=s?"".concat(s,"/email/event_settings"):"/email/event_settings",a=await fetch(o,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw m(e),Error("Failed to update email event settings")}let r=await a.json();return console.log("Update email event settings response:",r),r}catch(e){throw console.error("Failed to update email event settings:",e),e}},t8=async e=>{try{let t=s?"".concat(s,"/email/event_settings/reset"):"/email/event_settings/reset",o=await fetch(t,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error("Failed to reset email event settings")}let a=await o.json();return console.log("Reset email event settings response:",a),a}catch(e){throw console.error("Failed to reset email event settings:",e),e}},oe=async(e,t)=>{try{let o=s?"".concat(s,"/guardrails/").concat(t):"/guardrails/".concat(t),a=await fetch(o,{method:"DELETE",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw m(e),Error(e)}let r=await a.json();return console.log("Delete guardrail response:",r),r}catch(e){throw console.error("Failed to delete guardrail:",e),e}},ot=async e=>{try{let t=s?"".concat(s,"/guardrails/ui/add_guardrail_settings"):"/guardrails/ui/add_guardrail_settings",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error("Failed to get guardrail UI settings")}let a=await o.json();return console.log("Guardrail UI settings response:",a),a}catch(e){throw console.error("Failed to get guardrail UI settings:",e),e}},oo=async e=>{try{let t=s?"".concat(s,"/guardrails/ui/provider_specific_params"):"/guardrails/ui/provider_specific_params",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw m(e),Error("Failed to get guardrail provider specific parameters")}let a=await o.json();return console.log("Guardrail provider specific params response:",a),a}catch(e){throw console.error("Failed to get guardrail provider specific parameters:",e),e}},oa=async(e,t)=>{try{let o=s?"".concat(s,"/guardrails/").concat(t,"/info"):"/guardrails/".concat(t,"/info"),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw m(e),Error("Failed to get guardrail info")}let r=await a.json();return console.log("Guardrail info response:",r),r}catch(e){throw console.error("Failed to get guardrail info:",e),e}},or=async(e,t,o)=>{try{let a=s?"".concat(s,"/guardrails/").concat(t):"/guardrails/".concat(t),r=await fetch(a,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.text();throw m(e),Error("Failed to update guardrail")}let n=await r.json();return console.log("Update guardrail response:",n),n}catch(e){throw console.error("Failed to update guardrail:",e),e}},on=async e=>{try{let t=s?"".concat(s,"/get/sso_settings"):"/get/sso_settings";console.log("Fetching SSO configuration from:",t);let o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}let a=await o.json();return console.log("Fetched SSO configuration:",a),a}catch(e){throw console.error("Failed to fetch SSO configuration:",e),e}},ol=async(e,t)=>{try{let o=s?"".concat(s,"/update/sso_settings"):"/update/sso_settings";console.log("Updating SSO configuration:",t);let a=await fetch(o,{method:"PATCH",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=await a.json();return console.log("Updated SSO configuration:",r),r}catch(e){throw console.error("Failed to update SSO configuration:",e),e}},oc=async(e,t,o,a,r)=>{try{let t=s?"".concat(s,"/audit"):"/audit",o=new URLSearchParams;a&&o.append("page",a.toString()),r&&o.append("page_size",r.toString());let n=o.toString();n&&(t+="?".concat(n));let l=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!l.ok){let e=await l.json(),t=ov(e);throw m(t),Error(t)}return await l.json()}catch(e){throw console.error("Failed to fetch audit logs:",e),e}},oi=async e=>{try{let t=s?"".concat(s,"/user/available_users"):"/user/available_users",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e)}});if(!o.ok){if(404===o.status)return null;let e=await o.text();throw m(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to fetch remaining users:",e),e}},os=async(e,t,o)=>{try{let r=s?"".concat(s,"/config/pass_through_endpoint/").concat(encodeURIComponent(t)):"/config/pass_through_endpoint/".concat(encodeURIComponent(t)),n=await fetch(r,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok){let e=await n.json(),t=ov(e);throw m(t),Error(t)}let l=await n.json();return a.ZP.success("Pass through endpoint updated successfully"),l}catch(e){throw console.error("Failed to update pass through endpoint:",e),e}},od=async(e,t)=>{try{let o=s?"".concat(s,"/config/pass_through_endpoint?endpoint_id=").concat(encodeURIComponent(t)):"/config/pass_through_endpoint?endpoint_id=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}let r=(await a.json()).endpoints;if(!r||0===r.length)throw Error("Pass through endpoint not found");return r[0]}catch(e){throw console.error("Failed to get pass through endpoint info:",e),e}},ou=async(e,t)=>{try{let o=s?"".concat(s,"/config/callback/delete"):"/config/callback/delete",a=await fetch(o,{method:"POST",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({callback_name:t})});if(!a.ok){let e=await a.json(),t=ov(e);throw m(t),Error(t)}return await a.json()}catch(e){throw console.error("Failed to delete specific callback:",e),e}},oh=async e=>{let t=u(),o=await fetch("".concat(t,"/v1/mcp/tools"),{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw Error("HTTP error! status: ".concat(o.status));return await o.json()},op=async(e,t)=>{try{console.log("Testing MCP connection with config:",JSON.stringify(t));let a=s?"".concat(s,"/mcp-rest/test/connection"):"/mcp-rest/test/connection",r=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json",[f]:"Bearer ".concat(e)},body:JSON.stringify(t)}),n=r.headers.get("content-type");if(!n||!n.includes("application/json")){let e=await r.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(r.status,": ").concat(r.statusText,"). Check network tab for details."))}let l=await r.json();if(!r.ok||"error"===l.status){if("error"===l.status);else{var o;return{status:"error",message:(null===(o=l.error)||void 0===o?void 0:o.message)||"MCP connection test failed: ".concat(r.status," ").concat(r.statusText)}}}return l}catch(e){throw console.error("MCP connection test error:",e),e}},og=async(e,t)=>{try{console.log("Testing MCP tools list with config:",JSON.stringify(t));let o=s?"".concat(s,"/mcp-rest/test/tools/list"):"/mcp-rest/test/tools/list",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",[f]:"Bearer ".concat(e)},body:JSON.stringify(t)}),r=a.headers.get("content-type");if(!r||!r.includes("application/json")){let e=await a.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(a.status,": ").concat(a.statusText,"). Check network tab for details."))}let n=await a.json();if((!a.ok||n.error)&&!n.error)return{tools:[],error:"request_failed",message:n.message||"MCP tools list failed: ".concat(a.status," ").concat(a.statusText)};return n}catch(e){throw console.error("MCP tools list test error:",e),e}},om=async(e,t,o)=>{try{let a="".concat(u(),"/v1/vector_stores/").concat(t,"/search"),r=await fetch(a,{method:"POST",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({query:o})});if(!r.ok){let e=await r.text();return await m(e),null}return await r.json()}catch(e){throw console.error("Error testing vector store search:",e),e}},of=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:50,n=arguments.length>5?arguments[5]:void 0;try{let l=s?"".concat(s,"/tag/user-agent/analytics"):"/tag/user-agent/analytics",c=new URLSearchParams,i=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};c.append("start_date",i(t)),c.append("end_date",i(o)),c.append("page",a.toString()),c.append("page_size",r.toString()),n&&c.append("user_agent_filter",n);let d=c.toString();d&&(l+="?".concat(d));let u=await fetch(l,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!u.ok){let e=await u.json(),t=ov(e);throw m(t),Error(t)}return await u.json()}catch(e){throw console.error("Failed to fetch user agent analytics:",e),e}},ow=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/tag/dau"):"/tag/dau",n=new URLSearchParams;n.append("end_date",(e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)})(t)),a&&a.length>0?a.forEach(e=>{n.append("tag_filters",e)}):o&&n.append("tag_filter",o);let l=n.toString();l&&(r+="?".concat(l));let c=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}return await c.json()}catch(e){throw console.error("Failed to fetch DAU:",e),e}},oy=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/tag/wau"):"/tag/wau",n=new URLSearchParams;n.append("end_date",(e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)})(t)),a&&a.length>0?a.forEach(e=>{n.append("tag_filters",e)}):o&&n.append("tag_filter",o);let l=n.toString();l&&(r+="?".concat(l));let c=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}return await c.json()}catch(e){throw console.error("Failed to fetch WAU:",e),e}},oj=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/tag/mau"):"/tag/mau",n=new URLSearchParams;n.append("end_date",(e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)})(t)),a&&a.length>0?a.forEach(e=>{n.append("tag_filters",e)}):o&&n.append("tag_filter",o);let l=n.toString();l&&(r+="?".concat(l));let c=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}return await c.json()}catch(e){throw console.error("Failed to fetch MAU:",e),e}},o_=async e=>{try{let t=s?"".concat(s,"/tag/distinct"):"/tag/distinct",o=await fetch(t,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json(),t=ov(e);throw m(t),Error(t)}return await o.json()}catch(e){throw console.error("Failed to fetch distinct tags:",e),e}},oC=async(e,t,o,a)=>{try{let r=s?"".concat(s,"/tag/summary"):"/tag/summary",n=new URLSearchParams,l=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};n.append("start_date",l(t)),n.append("end_date",l(o)),a&&a.length>0&&a.forEach(e=>{n.append("tag_filters",e)});let c=n.toString();c&&(r+="?".concat(c));let i=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=ov(e);throw m(t),Error(t)}return await i.json()}catch(e){throw console.error("Failed to fetch user agent summary:",e),e}},ok=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:50,a=arguments.length>3?arguments[3]:void 0;try{let r=s?"".concat(s,"/tag/user-agent/per-user-analytics"):"/tag/user-agent/per-user-analytics",n=new URLSearchParams;n.append("page",t.toString()),n.append("page_size",o.toString()),a&&a.length>0&&a.forEach(e=>{n.append("tag_filters",e)});let l=n.toString();l&&(r+="?".concat(l));let c=await fetch(r,{method:"GET",headers:{[f]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!c.ok){let e=await c.json(),t=ov(e);throw m(t),Error(t)}return await c.json()}catch(e){throw console.error("Failed to fetch per-user analytics:",e),e}},ov=e=>(null==e?void 0:e.error)&&(e.error.message||e.error)||(null==e?void 0:e.message)||(null==e?void 0:e.detail)||(null==e?void 0:e.error)||JSON.stringify(e)},85968:function(e,t,o){o.d(t,{O:function(){return a}});let a=e=>{if(!e)return"An unknown error occurred";if("string"==typeof e)return e;if(e.message)try{let t=JSON.parse(e.message);if(t.error&&t.error.message)return t.error.message;return"string"==typeof t?t:JSON.stringify(t,null,2)}catch(t){return e.message}if(e.response&&e.response.data){if("string"==typeof e.response.data)return e.response.data;if(e.response.data.message)return e.response.data.message;if(e.response.data.error)return"string"==typeof e.response.data.error?e.response.data.error:e.response.data.error.message||JSON.stringify(e.response.data.error)}return String(e)}},3914:function(e,t,o){function a(){let e=window.location.hostname,t=["Lax","Strict","None"];["/","/ui"].forEach(o=>{document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,";"),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; domain=").concat(e,";"),t.forEach(t=>{let a="None"===t?" Secure;":"";document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; SameSite=").concat(t,";").concat(a),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; domain=").concat(e,"; SameSite=").concat(t,";").concat(a)})}),console.log("After clearing cookies:",document.cookie)}function r(e){let t=document.cookie.split("; ").find(t=>t.startsWith(e+"="));return t?t.split("=")[1]:null}o.d(t,{b:function(){return a},e:function(){return r}})}}]);