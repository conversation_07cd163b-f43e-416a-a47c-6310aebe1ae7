

2025-08-19 21:03:47,UID=0,Pro=0,Ultra=0,V=2025.08.18
OS:Microsoft Windows NT 10.0.26100.0
Office:Microsoft Office
Exception Message: 未能找到文件“D:\soft\BKOffice2025\Cookie.dat”。
Stack Trace:    在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath) 
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost) 
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost) 
   在 System.IO.File.InternalReadAllBytes(String path, Boolean checkHost) 
   在 Library.Common.Ini.GetCookie() 
--- 引发异常的上一位置中堆栈跟踪的末尾 --- 
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw() 
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=znPkK9EAcfqOQ48WdCayPdnpNDF8p(Object #=zlSvtK8U=) 
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zO5tDdzWo5aicWFggFnU36yg=(MethodBase #=zlSvtK8U=, Boolean #=zq66UUTg=) 
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zVPJ0WL2ewvlFXc3MupEH4I3wWTQ_() 
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zQwaMWa0$lry5Rbip2OVjOStMRs1B(Boolean #=zlSvtK8U=)
System.IO.FileNotFoundException: 未能找到文件“D:\soft\BKOffice2025\Cookie.dat”。
文件名:“D:\soft\BKOffice2025\Cookie.dat”
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.File.InternalReadAllBytes(String path, Boolean checkHost)
   在 Library.Common.Ini.GetCookie()
--- 引发异常的上一位置中堆栈跟踪的末尾 ---
   在 System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw()
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=znPkK9EAcfqOQ48WdCayPdnpNDF8p(Object #=zlSvtK8U=)
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zO5tDdzWo5aicWFggFnU36yg=(MethodBase #=zlSvtK8U=, Boolean #=zq66UUTg=)
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zVPJ0WL2ewvlFXc3MupEH4I3wWTQ_()
   在 #=q7m9djme6hGzrbHDkbR8qD53yjnnB8x_hwdFuiJAvf78=.#=zQwaMWa0$lry5Rbip2OVjOStMRs1B(Boolean #=zlSvtK8U=)


2025-08-23 08:54:54,UID=250982,Pro=0,Ultra=0,V=2025.08.18
OS:Microsoft Windows NT 10.0.26100.0
Office:Microsoft Office
Exception Message: 无法访问此集合中单独的行，因为表格有纵向合并的单元格。
Stack Trace:    在 Microsoft.Office.Interop.Word.Rows.get_Item(Int32 Index) 
   在 Library.WordAction.Button_sanxianbiao(IRibbonControl Control) 
   在 Library.WordAction.#=zD0jq6r$vTW7$fP3sGA==.#=zZS21L7$t3_GDMolvDJ2s864=()
System.Runtime.InteropServices.COMException (0x800A1767): 无法访问此集合中单独的行，因为表格有纵向合并的单元格。
   在 Microsoft.Office.Interop.Word.Rows.get_Item(Int32 Index)
   在 Library.WordAction.Button_sanxianbiao(IRibbonControl Control)
   在 Library.WordAction.#=zD0jq6r$vTW7$fP3sGA==.#=zZS21L7$t3_GDMolvDJ2s864=()
