import matplotlib.pyplot as plt
import networkx as nx

# 创建有向图
G = nx.DiGraph()

# 节点分类
upstream = ["招聘网站/平台", "测评工具", "培训教材供应商"]
midstream = ["招聘服务", "猎头服务", "培训与发展", "劳务派遣", "人事外包", "灵活用工平台"]
downstream = ["企业客户", "政府/公共部门"]
support = ["政策法规", "资本/投资机构", "科研院所", "行业协会"]

# 添加节点
for node in upstream + midstream + downstream + support:
    G.add_node(node)

# 添加上下游关系
edges = [
    ("招聘网站/平台", "招聘服务"),
    ("测评工具", "招聘服务"),
    ("培训教材供应商", "培训与发展"),
    ("招聘服务", "企业客户"),
    ("猎头服务", "企业客户"),
    ("培训与发展", "企业客户"),
    ("劳务派遣", "企业客户"),
    ("人事外包", "企业客户"),
    ("灵活用工平台", "企业客户"),
    ("招聘服务", "政府/公共部门"),
    ("人事外包", "政府/公共部门")
]
G.add_edges_from(edges)

# 支撑要素关系
for s in support:
    for m in midstream:
        G.add_edge(s, m)

# 布局
pos = nx.spring_layout(G, seed=42, k=0.7)

plt.figure(figsize=(14, 10))

# 节点颜色分类
color_map = []
for node in G.nodes():
    if node in upstream:
        color_map.append("#FFD700")  # 上游-金色
    elif node in midstream:
        color_map.append("#87CEEB")  # 中游-蓝色
    elif node in downstream:
        color_map.append("#32CD32")  # 下游-绿色
    else:
        color_map.append("#FF7F50")  # 支撑-橙色

# 绘制网络
nx.draw_networkx_nodes(G, pos, node_color=color_map, node_size=2500, edgecolors="black")
nx.draw_networkx_edges(G, pos, arrows=True, arrowstyle="->", arrowsize=15, edge_color="gray")
nx.draw_networkx_labels(G, pos, font_size=10, font_family="Microsoft YaHei")

plt.title("人力资源服务业产业地图示例", fontsize=16, fontweight="bold")
plt.axis("off")
plt.show()
