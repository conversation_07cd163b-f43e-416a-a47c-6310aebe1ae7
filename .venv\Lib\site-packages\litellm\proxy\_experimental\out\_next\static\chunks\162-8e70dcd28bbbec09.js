"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{36724:function(e,t,n){n.d(t,{Dx:function(){return i.Z},Zb:function(){return s.Z},xv:function(){return r.Z},zx:function(){return a.Z}});var a=n(20831),s=n(12514),r=n(84264),i=n(96761)},19130:function(e,t,n){n.d(t,{RM:function(){return s.Z},SC:function(){return l.Z},iA:function(){return a.Z},pj:function(){return r.Z},ss:function(){return i.Z},xs:function(){return o.Z}});var a=n(21626),s=n(97214),r=n(28241),i=n(58834),o=n(69552),l=n(71876)},88658:function(e,t,n){n.d(t,{L:function(){return s}});var a=n(49817);let s=e=>{let t;let{apiKeySource:n,accessToken:s,apiKey:r,inputMessage:i,chatHistory:o,selectedTags:l,selectedVectorStores:c,selectedGuardrails:d,endpointType:m,selectedModel:p,selectedSdk:u}=e,g="session"===n?s:r,x=window.location.origin,h=i||"Your prompt here",f=h.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n"),_=o.filter(e=>!e.isImage).map(e=>{let{role:t,content:n}=e;return{role:t,content:n}}),b={};l.length>0&&(b.tags=l),c.length>0&&(b.vector_stores=c),d.length>0&&(b.guardrails=d);let v=p||"your-model-name",j="azure"===u?'import openai\n\nclient = openai.AzureOpenAI(\n	api_key="'.concat(g||"YOUR_LITELLM_API_KEY",'",\n	azure_endpoint="').concat(x,'",\n	api_version="2024-02-01"\n)'):'import openai\n\nclient = openai.OpenAI(\n	api_key="'.concat(g||"YOUR_LITELLM_API_KEY",'",\n	base_url="').concat(x,'"\n)');switch(m){case a.KP.CHAT:{let e=Object.keys(b).length>0,n="";if(e){let e=JSON.stringify({metadata:b},null,2).split("\n").map(e=>" ".repeat(4)+e).join("\n").trim();n=",\n    extra_body=".concat(e)}let a=_.length>0?_:[{role:"user",content:h}];t='\nimport base64\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n    with open(image_path, "rb") as image_file:\n        return base64.b64encode(image_file.read()).decode(\'utf-8\')\n\n# Example with text only\nresponse = client.chat.completions.create(\n    model="'.concat(v,'",\n    messages=').concat(JSON.stringify(a,null,4)).concat(n,'\n)\n\nprint(response)\n\n# Example with image or PDF (uncomment and provide file path to use)\n# base64_file = encode_image("path/to/your/file.jpg")  # or .pdf\n# response_with_file = client.chat.completions.create(\n#     model="').concat(v,'",\n#     messages=[\n#         {\n#             "role": "user",\n#             "content": [\n#                 {\n#                     "type": "text",\n#                     "text": "').concat(f,'"\n#                 },\n#                 {\n#                     "type": "image_url",\n#                     "image_url": {\n#                         "url": f"data:image/jpeg;base64,{base64_file}"  # or data:application/pdf;base64,{base64_file}\n#                     }\n#                 }\n#             ]\n#         }\n#     ]').concat(n,"\n# )\n# print(response_with_file)\n");break}case a.KP.RESPONSES:{let e=Object.keys(b).length>0,n="";if(e){let e=JSON.stringify({metadata:b},null,2).split("\n").map(e=>" ".repeat(4)+e).join("\n").trim();n=",\n    extra_body=".concat(e)}let a=_.length>0?_:[{role:"user",content:h}];t='\nimport base64\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n    with open(image_path, "rb") as image_file:\n        return base64.b64encode(image_file.read()).decode(\'utf-8\')\n\n# Example with text only\nresponse = client.responses.create(\n    model="'.concat(v,'",\n    input=').concat(JSON.stringify(a,null,4)).concat(n,'\n)\n\nprint(response.output_text)\n\n# Example with image or PDF (uncomment and provide file path to use)\n# base64_file = encode_image("path/to/your/file.jpg")  # or .pdf\n# response_with_file = client.responses.create(\n#     model="').concat(v,'",\n#     input=[\n#         {\n#             "role": "user",\n#             "content": [\n#                 {"type": "input_text", "text": "').concat(f,'"},\n#                 {\n#                     "type": "input_image",\n#                     "image_url": f"data:image/jpeg;base64,{base64_file}",  # or data:application/pdf;base64,{base64_file}\n#                 },\n#             ],\n#         }\n#     ]').concat(n,"\n# )\n# print(response_with_file.output_text)\n");break}case a.KP.IMAGE:t="azure"===u?"\n# NOTE: The Azure SDK does not have a direct equivalent to the multi-modal 'responses.create' method shown for OpenAI.\n# This snippet uses 'client.images.generate' and will create a new image based on your prompt.\n# It does not use the uploaded image, as 'client.images.generate' does not support image inputs in this context.\nimport os\nimport requests\nimport json\nimport time\nfrom PIL import Image\n\nresult = client.images.generate(\n	model=\"".concat(v,'",\n	prompt="').concat(i,'",\n	n=1\n)\n\njson_response = json.loads(result.model_dump_json())\n\n# Set the directory for the stored image\nimage_dir = os.path.join(os.curdir, \'images\')\n\n# If the directory doesn\'t exist, create it\nif not os.path.isdir(image_dir):\n	os.mkdir(image_dir)\n\n# Initialize the image path\nimage_filename = f"generated_image_{int(time.time())}.png"\nimage_path = os.path.join(image_dir, image_filename)\n\ntry:\n	# Retrieve the generated image\n	if json_response.get("data") && len(json_response["data"]) > 0 && json_response["data"][0].get("url"):\n			image_url = json_response["data"][0]["url"]\n			generated_image = requests.get(image_url).content\n			with open(image_path, "wb") as image_file:\n					image_file.write(generated_image)\n\n			print(f"Image saved to {image_path}")\n			# Display the image\n			image = Image.open(image_path)\n			image.show()\n	else:\n			print("Could not find image URL in response.")\n			print("Full response:", json_response)\nexcept Exception as e:\n	print(f"An error occurred: {e}")\n	print("Full response:", json_response)\n'):"\nimport base64\nimport os\nimport time\nimport json\nfrom PIL import Image\nimport requests\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, \"rb\") as image_file:\n			return base64.b64encode(image_file.read()).decode('utf-8')\n\n# Helper function to create a file (simplified for this example)\ndef create_file(image_path):\n	# In a real implementation, this would upload the file to OpenAI\n	# For this example, we'll just return a placeholder ID\n	return f\"file_{os.path.basename(image_path).replace('.', '_')}\"\n\n# The prompt entered by the user\nprompt = \"".concat(f,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(v,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n');break;case a.KP.IMAGE_EDITS:t="azure"===u?'\nimport base64\nimport os\nimport time\nimport json\nfrom PIL import Image\nimport requests\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, "rb") as image_file:\n			return base64.b64encode(image_file.read()).decode(\'utf-8\')\n\n# The prompt entered by the user\nprompt = "'.concat(f,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(v,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n'):"\nimport base64\nimport os\nimport time\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, \"rb\") as image_file:\n			return base64.b64encode(image_file.read()).decode('utf-8')\n\n# Helper function to create a file (simplified for this example)\ndef create_file(image_path):\n	# In a real implementation, this would upload the file to OpenAI\n	# For this example, we'll just return a placeholder ID\n	return f\"file_{os.path.basename(image_path).replace('.', '_')}\"\n\n# The prompt entered by the user\nprompt = \"".concat(f,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(v,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n');break;default:t="\n# Code generation for this endpoint is not implemented yet."}return"".concat(j,"\n").concat(t)}},49817:function(e,t,n){var a,s,r,i;n.d(t,{KP:function(){return s},vf:function(){return l}}),(r=a||(a={})).IMAGE_GENERATION="image_generation",r.CHAT="chat",r.RESPONSES="responses",r.IMAGE_EDITS="image_edits",r.ANTHROPIC_MESSAGES="anthropic_messages",(i=s||(s={})).IMAGE="image",i.CHAT="chat",i.RESPONSES="responses",i.IMAGE_EDITS="image_edits",i.ANTHROPIC_MESSAGES="anthropic_messages";let o={image_generation:"image",chat:"chat",responses:"responses",image_edits:"image_edits",anthropic_messages:"anthropic_messages"},l=e=>{if(console.log("getEndpointType:",e),Object.values(a).includes(e)){let t=o[e];return console.log("endpointType:",t),t}return"chat"}},29488:function(e,t,n){n.d(t,{Hc:function(){return i},Ui:function(){return r},e4:function(){return o},xd:function(){return l}});let a="litellm_mcp_auth_tokens",s=()=>{try{let e=localStorage.getItem(a);return e?JSON.parse(e):{}}catch(e){return console.error("Error reading MCP auth tokens from localStorage:",e),{}}},r=(e,t)=>{try{let n=s()[e];if(n&&n.serverAlias===t||n&&!t&&!n.serverAlias)return n.authValue;return null}catch(e){return console.error("Error getting MCP auth token:",e),null}},i=(e,t,n,r)=>{try{let i=s();i[e]={serverId:e,serverAlias:r,authValue:t,authType:n,timestamp:Date.now()},localStorage.setItem(a,JSON.stringify(i))}catch(e){console.error("Error storing MCP auth token:",e)}},o=e=>{try{let t=s();delete t[e],localStorage.setItem(a,JSON.stringify(t))}catch(e){console.error("Error removing MCP auth token:",e)}},l=()=>{try{localStorage.removeItem(a)}catch(e){console.error("Error clearing MCP auth tokens:",e)}}},8048:function(e,t,n){n.d(t,{C:function(){return m}});var a=n(57437),s=n(71594),r=n(24525),i=n(2265),o=n(19130),l=n(44633),c=n(86462),d=n(49084);function m(e){let{data:t=[],columns:n,isLoading:m=!1,table:p,defaultSorting:u=[]}=e,[g,x]=i.useState(u),[h]=i.useState("onChange"),[f,_]=i.useState({}),[b,v]=i.useState({}),j=(0,s.b7)({data:t,columns:n,state:{sorting:g,columnSizing:f,columnVisibility:b},columnResizeMode:h,onSortingChange:x,onColumnSizingChange:_,onColumnVisibilityChange:v,getCoreRowModel:(0,r.sC)(),getSortedRowModel:(0,r.tj)(),enableSorting:!0,enableColumnResizing:!0,defaultColumn:{minSize:40,maxSize:500}});return i.useEffect(()=>{p&&(p.current=j)},[j,p]),(0,a.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)("div",{className:"relative min-w-full",children:(0,a.jsxs)(o.iA,{className:"[&_td]:py-2 [&_th]:py-2 w-full",children:[(0,a.jsx)(o.ss,{children:j.getHeaderGroups().map(e=>(0,a.jsx)(o.SC,{children:e.headers.map(e=>{var t;return(0,a.jsxs)(o.xs,{className:"py-1 h-8 relative ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""," ").concat((null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.className)||""),style:{width:"actions"===e.id?120:e.getSize(),position:"actions"===e.id?"sticky":"relative",right:"actions"===e.id?0:"auto"},onClick:e.column.getCanSort()?e.column.getToggleSortingHandler():void 0,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,s.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&e.column.getCanSort()&&(0,a.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,a.jsx)(l.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,a.jsx)(c.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,a.jsx)(d.Z,{className:"h-4 w-4 text-gray-400"})})]}),e.column.getCanResize()&&(0,a.jsx)("div",{onMouseDown:e.getResizeHandler(),onTouchStart:e.getResizeHandler(),className:"absolute right-0 top-0 h-full w-2 cursor-col-resize select-none touch-none ".concat(e.column.getIsResizing()?"bg-blue-500":"hover:bg-blue-200")})]},e.id)})},e.id))}),(0,a.jsx)(o.RM,{children:m?(0,a.jsx)(o.SC,{children:(0,a.jsx)(o.pj,{colSpan:n.length,className:"h-8 text-center",children:(0,a.jsx)("div",{className:"text-center text-gray-500",children:(0,a.jsx)("p",{children:"\uD83D\uDE85 Loading models..."})})})}):j.getRowModel().rows.length>0?j.getRowModel().rows.map(e=>(0,a.jsx)(o.SC,{children:e.getVisibleCells().map(e=>{var t;return(0,a.jsx)(o.pj,{className:"py-0.5 ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""," ").concat((null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.className)||""),style:{width:"actions"===e.column.id?120:e.column.getSize(),position:"actions"===e.column.id?"sticky":"relative",right:"actions"===e.column.id?0:"auto"},children:(0,s.ie)(e.column.columnDef.cell,e.getContext())},e.id)})},e.id)):(0,a.jsx)(o.SC,{children:(0,a.jsx)(o.pj,{colSpan:n.length,className:"h-8 text-center",children:(0,a.jsx)("div",{className:"text-center text-gray-500",children:(0,a.jsx)("p",{children:"No models found"})})})})})]})})})})}},65373:function(e,t,n){n.d(t,{Z:function(){return _}});var a=n(57437),s=n(27648),r=n(2265),i=n(89970),o=n(80795),l=n(19250),c=n(15883),d=n(46346),m=n(57400),p=n(91870),u=n(40428),g=n(3914);let x=async e=>{if(!e)return null;try{return await (0,l.getProxyUISettings)(e)}catch(e){return console.error("Error fetching proxy settings:",e),null}};var h=n(69734),f=n(29488),_=e=>{let{userID:t,userEmail:n,userRole:_,premiumUser:b,proxySettings:v,setProxySettings:j,accessToken:y,isPublicPage:N=!1}=e,w=(0,l.getProxyBaseUrl)(),[S,A]=(0,r.useState)(""),{logoUrl:I}=(0,h.F)();(0,r.useEffect)(()=>{(async()=>{if(y){let e=await x(y);console.log("response from fetchProxySettings",e),e&&j(e)}})()},[y]),(0,r.useEffect)(()=>{A((null==v?void 0:v.PROXY_LOGOUT_URL)||"")},[v]);let k=[{key:"user-info",label:(0,a.jsxs)("div",{className:"px-3 py-3 border-b border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.Z,{className:"mr-2 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-semibold text-gray-900",children:t})]}),b?(0,a.jsx)(i.Z,{title:"Premium User",placement:"left",children:(0,a.jsxs)("div",{className:"flex items-center bg-gradient-to-r from-amber-500 to-yellow-500 text-white px-2 py-0.5 rounded-full cursor-help",children:[(0,a.jsx)(d.Z,{className:"mr-1 text-xs"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Premium"})]})}):(0,a.jsx)(i.Z,{title:"Upgrade to Premium for advanced features",placement:"left",children:(0,a.jsxs)("div",{className:"flex items-center bg-gray-100 text-gray-500 px-2 py-0.5 rounded-full cursor-help",children:[(0,a.jsx)(d.Z,{className:"mr-1 text-xs"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Standard"})]})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[(0,a.jsx)(m.Z,{className:"mr-2 text-gray-400 text-xs"}),(0,a.jsx)("span",{className:"text-gray-500 text-xs",children:"Role"}),(0,a.jsx)("span",{className:"ml-auto text-gray-700 font-medium",children:_})]}),(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[(0,a.jsx)(p.Z,{className:"mr-2 text-gray-400 text-xs"}),(0,a.jsx)("span",{className:"text-gray-500 text-xs",children:"Email"}),(0,a.jsx)("span",{className:"ml-auto text-gray-700 font-medium truncate max-w-[150px]",title:n||"Unknown",children:n||"Unknown"})]})]})]})},{key:"logout",label:(0,a.jsxs)("div",{className:"flex items-center py-2 px-3 hover:bg-gray-50 rounded-md mx-1 my-1",onClick:()=>{(0,g.b)(),(0,f.xd)(),window.location.href=S},children:[(0,a.jsx)(u.Z,{className:"mr-3 text-gray-600"}),(0,a.jsx)("span",{className:"text-gray-800",children:"Logout"})]})}];return(0,a.jsx)("nav",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center h-12 px-4",children:[(0,a.jsx)("div",{className:"flex items-center flex-shrink-0",children:(0,a.jsx)(s.default,{href:"/",className:"flex items-center",children:(0,a.jsx)("img",{src:I||"".concat(w,"/get_image"),alt:"LiteLLM Brand",className:"h-8 w-auto"})})}),(0,a.jsxs)("div",{className:"flex items-center space-x-5 ml-auto",children:[(0,a.jsx)("a",{href:"https://docs.litellm.ai/docs/",target:"_blank",rel:"noopener noreferrer",className:"text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:"Docs"}),!N&&(0,a.jsx)(o.Z,{menu:{items:k,className:"min-w-[200px]",style:{padding:"8px",marginTop:"8px",borderRadius:"12px",boxShadow:"0 4px 24px rgba(0, 0, 0, 0.08)"}},overlayStyle:{minWidth:"200px"},children:(0,a.jsxs)("button",{className:"inline-flex items-center text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:["User",(0,a.jsx)("svg",{className:"ml-1 w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M19 9l-7 7-7-7"})})]})})]})]})})})}},42673:function(e,t,n){var a,s;n.d(t,{Cl:function(){return a},bK:function(){return d},cd:function(){return o},dr:function(){return l},fK:function(){return r},ph:function(){return c}}),n(2265),(s=a||(a={})).Bedrock="Amazon Bedrock",s.Anthropic="Anthropic",s.AssemblyAI="AssemblyAI",s.SageMaker="AWS SageMaker",s.Azure="Azure",s.Azure_AI_Studio="Azure AI Foundry (Studio)",s.Cerebras="Cerebras",s.Cohere="Cohere",s.Databricks="Databricks",s.DeepInfra="DeepInfra",s.Deepgram="Deepgram",s.Deepseek="Deepseek",s.ElevenLabs="ElevenLabs",s.FireworksAI="Fireworks AI",s.Google_AI_Studio="Google AI Studio",s.GradientAI="GradientAI",s.Groq="Groq",s.JinaAI="Jina AI",s.MistralAI="Mistral AI",s.Ollama="Ollama",s.OpenAI="OpenAI",s.OpenAI_Compatible="OpenAI-Compatible Endpoints (Together AI, etc.)",s.OpenAI_Text="OpenAI Text Completion",s.OpenAI_Text_Compatible="OpenAI-Compatible Text Completion Models (Together AI, etc.)",s.Openrouter="Openrouter",s.Perplexity="Perplexity",s.Sambanova="Sambanova",s.TogetherAI="TogetherAI",s.Triton="Triton",s.Vertex_AI="Vertex AI (Anthropic, Gemini, etc.)",s.VolcEngine="VolcEngine",s.Voyage="Voyage AI",s.xAI="xAI";let r={OpenAI:"openai",OpenAI_Text:"text-completion-openai",Azure:"azure",Azure_AI_Studio:"azure_ai",Anthropic:"anthropic",Google_AI_Studio:"gemini",Bedrock:"bedrock",Groq:"groq",MistralAI:"mistral",Cohere:"cohere",OpenAI_Compatible:"openai",OpenAI_Text_Compatible:"text-completion-openai",Vertex_AI:"vertex_ai",Databricks:"databricks",xAI:"xai",Deepseek:"deepseek",Ollama:"ollama",AssemblyAI:"assemblyai",Cerebras:"cerebras",Sambanova:"sambanova",Perplexity:"perplexity",TogetherAI:"together_ai",Openrouter:"openrouter",FireworksAI:"fireworks_ai",GradientAI:"gradient_ai",Triton:"triton",Deepgram:"deepgram",ElevenLabs:"elevenlabs",SageMaker:"sagemaker_chat",Voyage:"voyage",JinaAI:"jina_ai",VolcEngine:"volcengine",DeepInfra:"deepinfra"},i="/ui/assets/logos/",o={Anthropic:"".concat(i,"anthropic.svg"),AssemblyAI:"".concat(i,"assemblyai_small.png"),Azure:"".concat(i,"microsoft_azure.svg"),"Azure AI Foundry (Studio)":"".concat(i,"microsoft_azure.svg"),"Amazon Bedrock":"".concat(i,"bedrock.svg"),"AWS SageMaker":"".concat(i,"bedrock.svg"),Cerebras:"".concat(i,"cerebras.svg"),Cohere:"".concat(i,"cohere.svg"),Databricks:"".concat(i,"databricks.svg"),Deepseek:"".concat(i,"deepseek.svg"),"Fireworks AI":"".concat(i,"fireworks.svg"),Groq:"".concat(i,"groq.svg"),"Google AI Studio":"".concat(i,"google.svg"),"Mistral AI":"".concat(i,"mistral.svg"),Ollama:"".concat(i,"ollama.svg"),OpenAI:"".concat(i,"openai_small.svg"),"OpenAI Text Completion":"".concat(i,"openai_small.svg"),"OpenAI-Compatible Text Completion Models (Together AI, etc.)":"".concat(i,"openai_small.svg"),"OpenAI-Compatible Endpoints (Together AI, etc.)":"".concat(i,"openai_small.svg"),Openrouter:"".concat(i,"openrouter.svg"),Perplexity:"".concat(i,"perplexity-ai.svg"),Sambanova:"".concat(i,"sambanova.svg"),TogetherAI:"".concat(i,"togetherai.svg"),"Vertex AI (Anthropic, Gemini, etc.)":"".concat(i,"google.svg"),xAI:"".concat(i,"xai.svg"),GradientAI:"".concat(i,"gradientai.svg"),Triton:"".concat(i,"nvidia_triton.png"),Deepgram:"".concat(i,"deepgram.png"),ElevenLabs:"".concat(i,"elevenlabs.png"),"Voyage AI":"".concat(i,"voyage.webp"),"Jina AI":"".concat(i,"jina.png"),VolcEngine:"".concat(i,"volcengine.png"),DeepInfra:"".concat(i,"deepinfra.png")},l=e=>{if(!e)return{logo:"",displayName:"-"};if("gemini"===e.toLowerCase()){let e="Google AI Studio";return{logo:o[e],displayName:e}}let t=Object.keys(r).find(t=>r[t].toLowerCase()===e.toLowerCase());if(!t)return{logo:"",displayName:e};let n=a[t];return{logo:o[n],displayName:n}},c=e=>{if("Vertex AI (Anthropic, Gemini, etc.)"===e)return"gemini-pro";if("Anthropic"==e||"Amazon Bedrock"==e)return"claude-3-opus";if("AWS SageMaker"==e)return"sagemaker/jumpstart-dft-meta-textgeneration-llama-2-7b";if("Google AI Studio"==e)return"gemini-pro";if("Azure AI Foundry (Studio)"==e)return"azure_ai/command-r-plus";if("Azure"==e)return"azure/my-deployment";else if("Voyage AI"==e)return"voyage/";else if("Jina AI"==e)return"jina_ai/";else if("VolcEngine"==e)return"volcengine/<any-model-on-volcengine>";else if("DeepInfra"==e)return"deepinfra/<any-model-on-deepinfra>";else return"gpt-3.5-turbo"},d=(e,t)=>{console.log("Provider key: ".concat(e));let n=r[e];console.log("Provider mapped to: ".concat(n));let a=[];return e&&"object"==typeof t&&(Object.entries(t).forEach(e=>{let[t,s]=e;null!==s&&"object"==typeof s&&"litellm_provider"in s&&(s.litellm_provider===n||s.litellm_provider.includes(n))&&a.push(t)}),"Cohere"==e&&(console.log("Adding cohere chat models"),Object.entries(t).forEach(e=>{let[t,n]=e;null!==n&&"object"==typeof n&&"litellm_provider"in n&&"cohere_chat"===n.litellm_provider&&a.push(t)})),"AWS SageMaker"==e&&(console.log("Adding sagemaker chat models"),Object.entries(t).forEach(e=>{let[t,n]=e;null!==n&&"object"==typeof n&&"litellm_provider"in n&&"sagemaker_chat"===n.litellm_provider&&a.push(t)}))),a}},72162:function(e,t,n){var a=n(57437),s=n(2265),r=n(19250),i=n(8048),o=n(36724),l=n(42264),c=n(89970),d=n(3810),m=n(52787),p=n(82680),u=n(3477),g=n(17732),x=n(33245),h=n(78867),f=n(88658),_=n(49817),b=n(42673),v=n(65373),j=n(69734);t.Z=e=>{var t,n;let{accessToken:y}=e,[N,w]=(0,s.useState)(null),[S,A]=(0,s.useState)("LiteLLM Gateway"),[I,k]=(0,s.useState)(null),[C,E]=(0,s.useState)(""),[O,M]=(0,s.useState)({}),[T,D]=(0,s.useState)(!0),[z,P]=(0,s.useState)(""),[L,Z]=(0,s.useState)([]),[G,R]=(0,s.useState)([]),[H,F]=(0,s.useState)([]),[K,U]=(0,s.useState)("I'm alive! ✓"),[V,W]=(0,s.useState)(!1),[q,J]=(0,s.useState)(null),[B,Y]=(0,s.useState)({}),$=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=async()=>{try{D(!0);let e=await (0,r.modelHubPublicModelsCall)();console.log("ModelHubData:",e),w(e)}catch(e){console.error("There was an error fetching the public model data",e),U("Service unavailable")}finally{D(!1)}};(async()=>{let e=await (0,r.getPublicModelHubInfo)();console.log("Public Model Hub Info:",e),A(e.docs_title),k(e.custom_docs_description),E(e.litellm_version),M(e.useful_links||{})})(),e()},[]),(0,s.useEffect)(()=>{},[z,L,G,H]);let X=(0,s.useMemo)(()=>{if(!N)return[];let e=N;if(z.trim()){let t=z.toLowerCase(),n=t.split(/\s+/),a=N.filter(e=>{let a=e.model_group.toLowerCase();return!!a.includes(t)||n.every(e=>a.includes(e))});a.length>0&&(e=a.sort((e,n)=>{let a=e.model_group.toLowerCase(),s=n.model_group.toLowerCase(),r=a===t?1e3:0,i=s===t?1e3:0,o=a.startsWith(t)?100:0,l=s.startsWith(t)?100:0,c=t.split(/\s+/).every(e=>a.includes(e))?50:0,d=t.split(/\s+/).every(e=>s.includes(e))?50:0,m=a.length;return i+l+d+(1e3-s.length)-(r+o+c+(1e3-m))}))}return e.filter(e=>{let t=0===L.length||L.some(t=>e.providers.includes(t)),n=0===G.length||G.includes(e.mode||""),a=0===H.length||Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).some(e=>{let[t]=e,n=t.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return H.includes(n)});return t&&n&&a})},[N,z,L,G,H]),Q=e=>{J(e),W(!0)},ee=e=>{navigator.clipboard.writeText(e),l.ZP.success("Copied to clipboard!")},et=e=>e.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),en=e=>Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).map(e=>{let[t]=e;return t}),ea=e=>"$".concat((1e6*e).toFixed(4)),es=e=>e?e>=1e3?"".concat((e/1e3).toFixed(0),"K"):e.toString():"N/A",er=(e,t)=>{let n=[];return e&&n.push("RPM: ".concat(e.toLocaleString())),t&&n.push("TPM: ".concat(t.toLocaleString())),n.length>0?n.join(", "):"N/A"};return(0,a.jsx)(j.f,{accessToken:y,children:(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(v.Z,{userID:null,userEmail:null,userRole:null,premiumUser:!1,setProxySettings:Y,proxySettings:B,accessToken:y||null,isPublicPage:!0}),(0,a.jsxs)("div",{className:"w-full px-8 py-12",children:[(0,a.jsxs)(o.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,a.jsx)(o.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"About"}),(0,a.jsx)("p",{className:"text-gray-700 mb-6 text-base leading-relaxed",children:I||"Proxy Server to call 100+ LLMs in the OpenAI format."}),(0,a.jsx)("div",{className:"flex items-center space-x-3 text-sm text-gray-600",children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-4 h-4 mr-2",children:"\uD83D\uDD27"}),"Built with litellm: v",C]})})]}),O&&Object.keys(O).length>0&&(0,a.jsxs)(o.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,a.jsx)(o.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"Useful Links"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.entries(O||{}).map(e=>{let[t,n]=e;return(0,a.jsxs)("button",{onClick:()=>window.open(n,"_blank"),className:"flex items-center space-x-3 text-blue-600 hover:text-blue-800 transition-colors p-3 rounded-lg hover:bg-blue-50 border border-gray-200",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),(0,a.jsx)(o.xv,{className:"text-sm font-medium",children:t})]},t)})})]}),(0,a.jsxs)(o.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,a.jsx)(o.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"Health and Endpoint Status"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,a.jsxs)(o.xv,{className:"text-green-600 font-medium text-sm",children:["Service status: ",K]})})]}),(0,a.jsxs)(o.Zb,{className:"p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-8",children:(0,a.jsx)(o.Dx,{className:"text-2xl font-semibold text-gray-900",children:"Available Models"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)(o.xv,{className:"text-sm font-medium text-gray-700",children:"Search Models:"}),(0,a.jsx)(c.Z,{title:"Smart search with relevance ranking - finds models containing your search terms, ranked by relevance. Try searching 'xai grok-4', 'claude-4', 'gpt-4', or 'sonnet'",placement:"top",children:(0,a.jsx)(x.Z,{className:"w-4 h-4 text-gray-400 cursor-help"})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.Z,{className:"w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,a.jsx)("input",{type:"text",placeholder:"Search model names... (smart search enabled)",value:z,onChange:e=>P(e.target.value),className:"border border-gray-300 rounded-lg pl-10 pr-4 py-2 w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Provider:"}),(0,a.jsx)(m.default,{mode:"multiple",value:L,onChange:e=>Z(e),placeholder:"Select providers",className:"w-full",size:"large",allowClear:!0,optionRender:e=>{let{logo:t}=(0,b.dr)(e.value);return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,a.jsx)("img",{src:t,alt:e.label,className:"w-5 h-5 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,a.jsx)("span",{className:"capitalize",children:e.label})]})},children:N&&(e=>{let t=new Set;return e.forEach(e=>{e.providers.forEach(e=>t.add(e))}),Array.from(t)})(N).map(e=>(0,a.jsx)(m.default.Option,{value:e,children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Mode:"}),(0,a.jsx)(m.default,{mode:"multiple",value:G,onChange:e=>R(e),placeholder:"Select modes",className:"w-full",size:"large",allowClear:!0,children:N&&(e=>{let t=new Set;return e.forEach(e=>{e.mode&&t.add(e.mode)}),Array.from(t)})(N).map(e=>(0,a.jsx)(m.default.Option,{value:e,children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Features:"}),(0,a.jsx)(m.default,{mode:"multiple",value:H,onChange:e=>F(e),placeholder:"Select features",className:"w-full",size:"large",allowClear:!0,children:N&&(e=>{let t=new Set;return e.forEach(e=>{Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).forEach(e=>{let[n]=e,a=n.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");t.add(a)})}),Array.from(t).sort()})(N).map(e=>(0,a.jsx)(m.default.Option,{value:e,children:e},e))})]})]}),(0,a.jsx)(i.C,{columns:[{header:"Model Name",accessorKey:"model_group",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsx)(c.Z,{title:t.original.model_group,children:(0,a.jsx)(o.zx,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left",onClick:()=>Q(t.original),children:t.original.model_group})})})},size:150},{header:"Providers",accessorKey:"providers",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.providers;return(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:n.map(e=>{let{logo:t}=(0,b.dr)(e);return(0,a.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded text-xs",children:[t&&(0,a.jsx)("img",{src:t,alt:e,className:"w-3 h-3 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,a.jsx)("span",{className:"capitalize",children:e})]},e)})})},size:120},{header:"Mode",accessorKey:"mode",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.mode;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:(e=>{switch(null==e?void 0:e.toLowerCase()){case"chat":return"\uD83D\uDCAC";case"rerank":return"\uD83D\uDD04";case"embedding":return"\uD83D\uDCC4";default:return"\uD83E\uDD16"}})(n||"")}),(0,a.jsx)(o.xv,{children:n||"Chat"})]})},size:100},{header:"Max Input",accessorKey:"max_input_tokens",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,a.jsx)(o.xv,{className:"text-center",children:es(t.original.max_input_tokens)})},size:100,meta:{className:"text-center"}},{header:"Max Output",accessorKey:"max_output_tokens",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,a.jsx)(o.xv,{className:"text-center",children:es(t.original.max_output_tokens)})},size:100,meta:{className:"text-center"}},{header:"Input $/1M",accessorKey:"input_cost_per_token",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.input_cost_per_token;return(0,a.jsx)(o.xv,{className:"text-center",children:n?ea(n):"Free"})},size:100,meta:{className:"text-center"}},{header:"Output $/1M",accessorKey:"output_cost_per_token",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.output_cost_per_token;return(0,a.jsx)(o.xv,{className:"text-center",children:n?ea(n):"Free"})},size:100,meta:{className:"text-center"}},{header:"Features",accessorKey:"supports_vision",enableSorting:!1,cell:e=>{let{row:t}=e,n=Object.entries(t.original).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).map(e=>{let[t]=e;return et(t)});return 0===n.length?(0,a.jsx)(o.xv,{className:"text-gray-400",children:"-"}):1===n.length?(0,a.jsx)("div",{className:"h-6 flex items-center",children:(0,a.jsx)(d.Z,{color:"blue",className:"text-xs",children:n[0]})}):(0,a.jsxs)("div",{className:"h-6 flex items-center space-x-1",children:[(0,a.jsx)(d.Z,{color:"blue",className:"text-xs",children:n[0]}),(0,a.jsx)(c.Z,{title:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-medium",children:"All Features:"}),n.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs",children:["• ",e]},t))]}),trigger:"click",placement:"topLeft",children:(0,a.jsxs)("span",{className:"text-xs text-blue-600 cursor-pointer hover:text-blue-800 hover:underline",onClick:e=>e.stopPropagation(),children:["+",n.length-1]})})]})},size:120},{header:"Limits",accessorKey:"rpm",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original;return(0,a.jsx)(o.xv,{className:"text-xs text-gray-600",children:er(n.rpm,n.tpm)})},size:150}],data:X,isLoading:T,table:$,defaultSorting:[{id:"model_group",desc:!1}]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(o.xv,{className:"text-sm text-gray-600",children:["Showing ",X.length," of ",(null==N?void 0:N.length)||0," models"]})})]})]}),(0,a.jsx)(p.Z,{title:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:(null==q?void 0:q.model_group)||"Model Details"}),q&&(0,a.jsx)(c.Z,{title:"Copy model name",children:(0,a.jsx)(h.Z,{onClick:()=>ee(q.model_group),className:"cursor-pointer text-gray-500 hover:text-blue-500 w-4 h-4"})})]}),width:1e3,open:V,footer:null,onOk:()=>{W(!1),J(null)},onCancel:()=>{W(!1),J(null)},children:q&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Model Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Model Name:"}),(0,a.jsx)(o.xv,{children:q.model_group})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Mode:"}),(0,a.jsx)(o.xv,{children:q.mode||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Providers:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:q.providers.map(e=>{let{logo:t}=(0,b.dr)(e);return(0,a.jsx)(d.Z,{color:"blue",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&(0,a.jsx)("img",{src:t,alt:e,className:"w-3 h-3 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,a.jsx)("span",{className:"capitalize",children:e})]})},e)})})]})]}),q.model_group.includes("*")&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium text-blue-900 mb-2",children:"Wildcard Routing"}),(0,a.jsxs)(o.xv,{className:"text-sm text-blue-800 mb-2",children:["This model uses wildcard routing. You can pass any value where you see the ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:"*"})," symbol."]}),(0,a.jsxs)(o.xv,{className:"text-sm text-blue-800",children:["For example, with ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:q.model_group}),", you can use any string (",(0,a.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:q.model_group.replace("*","my-custom-value")}),") that matches this pattern."]})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Token & Cost Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Max Input Tokens:"}),(0,a.jsx)(o.xv,{children:(null===(t=q.max_input_tokens)||void 0===t?void 0:t.toLocaleString())||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Max Output Tokens:"}),(0,a.jsx)(o.xv,{children:(null===(n=q.max_output_tokens)||void 0===n?void 0:n.toLocaleString())||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Input Cost per 1M Tokens:"}),(0,a.jsx)(o.xv,{children:q.input_cost_per_token?ea(q.input_cost_per_token):"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Output Cost per 1M Tokens:"}),(0,a.jsx)(o.xv,{children:q.output_cost_per_token?ea(q.output_cost_per_token):"Not specified"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Capabilities"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let e=en(q),t=["green","blue","purple","orange","red","yellow"];return 0===e.length?(0,a.jsx)(o.xv,{className:"text-gray-500",children:"No special capabilities listed"}):e.map((e,n)=>(0,a.jsx)(d.Z,{color:t[n%t.length],children:et(e)},e))})()})]}),(q.tpm||q.rpm)&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Rate Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[q.tpm&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Tokens per Minute:"}),(0,a.jsx)(o.xv,{children:q.tpm.toLocaleString()})]}),q.rpm&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"font-medium",children:"Requests per Minute:"}),(0,a.jsx)(o.xv,{children:q.rpm.toLocaleString()})]})]})]}),q.supported_openai_params&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Supported OpenAI Parameters"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:q.supported_openai_params.map(e=>(0,a.jsx)(d.Z,{color:"green",children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.xv,{className:"text-lg font-semibold mb-4",children:"Usage Example"}),(0,a.jsx)("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto",children:(0,a.jsx)("pre",{className:"text-sm",children:(0,f.L)({apiKeySource:"custom",accessToken:null,apiKey:"your_api_key",inputMessage:"Hello, how are you?",chatHistory:[{role:"user",content:"Hello, how are you?",isImage:!1}],selectedTags:[],selectedVectorStores:[],selectedGuardrails:[],endpointType:(0,_.vf)(q.mode||"chat"),selectedModel:q.model_group,selectedSdk:"openai"})})}),(0,a.jsx)("div",{className:"mt-2 text-right",children:(0,a.jsx)("button",{onClick:()=>{ee((0,f.L)({apiKeySource:"custom",accessToken:null,apiKey:"your_api_key",inputMessage:"Hello, how are you?",chatHistory:[{role:"user",content:"Hello, how are you?",isImage:!1}],selectedTags:[],selectedVectorStores:[],selectedGuardrails:[],endpointType:(0,_.vf)(q.mode||"chat"),selectedModel:q.model_group,selectedSdk:"openai"}))},className:"text-sm text-blue-600 hover:text-blue-800 cursor-pointer",children:"Copy to clipboard"})})]})]})})]})})}},69734:function(e,t,n){n.d(t,{F:function(){return o},f:function(){return l}});var a=n(57437),s=n(2265),r=n(19250);let i=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(i);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},l=e=>{let{children:t,accessToken:n}=e,[o,l]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{if(n)try{let t=(0,r.getProxyBaseUrl)(),a=await fetch(t?"".concat(t,"/get/ui_theme_settings"):"/get/ui_theme_settings",{method:"GET",headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}});if(a.ok){var e;let t=await a.json();(null===(e=t.values)||void 0===e?void 0:e.logo_url)&&l(t.values.logo_url)}}catch(e){console.warn("Failed to load logo settings from backend:",e)}})()},[n]),(0,a.jsx)(i.Provider,{value:{logoUrl:o,setLogoUrl:l},children:t})}}}]);